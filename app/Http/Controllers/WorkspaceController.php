<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreWorkspaceRequest;
use App\Http\Requests\UpdateWorkspaceRequest;
use App\Http\Resources\WorkspaceResource;
use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WorkspaceController extends Controller
{
    public function __construct(
        protected WorkspaceService $workspaceService
    ) {
    }

    /**
     * 显示工作空间列表页面
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // 获取用户拥有的工作空间
        $ownedWorkspaces = $user->workspaces()->with('cluster')->get();

        // 获取用户作为成员的工作空间
        $memberWorkspaces = $user->memberWorkspaces()->with('cluster')->get();

        // 合并所有工作空间并去重
        $allWorkspaces = $ownedWorkspaces->merge($memberWorkspaces)->unique('id');

        // 如果是 API 请求，返回 JSON
        if ($request->expectsJson()) {
            return response()->json([
                'data' => WorkspaceResource::collection($allWorkspaces)->resolve(),
                'currentWorkspace' => $user->currentWorkspace ? new WorkspaceResource($user->currentWorkspace) : null,
            ]);
        }

        return Inertia::render('Workspaces/Index', [
            'workspaces' => WorkspaceResource::collection($allWorkspaces)->resolve(),
        ]);
    }

    /**
     * 显示创建工作空间页面
     */
    public function create(Request $request)
    {
        $user = $request->user();

        $clusters = Cluster::select('id', 'name')->get()->map(function ($cluster) {
            return [
                'id' => $cluster->id,
                'name' => $cluster->name,
                'billing_enabled' => $cluster->isBillingEnabled(),
            ];
        });

        return Inertia::render('Workspaces/Create', [
            'clusters' => $clusters,
            'balance_not_enough' => !$user->hasEnoughBalance('1'),
        ]);
    }

    /**
     * 存储新的工作空间
     */
    public function store(StoreWorkspaceRequest $request)
    {
        $user = $request->user();

        // 如果用户没有余额，则不允许创建
        if (!$user->hasEnoughBalance('1')) {
            return redirect()->route('balance.index')->with('error', '你的账户余额不足 1，无法创建工作空间');
        }

        try {
            $workspace = $this->workspaceService->createWorkspace([
                'user_id' => auth()->id(),
                'cluster_id' => $request->cluster_id,
                'name' => $request->name,
            ]);

            // 将团队创建人也加入团队成员列表
            $workspace->users()->attach($user->id);

            // 设置权限为 team owner
            $user->switchWorkspaceTemp($workspace);
            $user->assignRole('team owner');

            // 如果用户没有默认团队
            if (!$user->current_workspace_id) {
                $user->setCurrentWorkspace($workspace);
            }

            return redirect()->route('workspaces.show', $workspace)
                ->with('success', '工作空间创建成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => '创建工作空间失败：' . $e->getMessage()]);
        }
    }

    /**
     * 显示工作空间详情
     */
    public function show(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        $workspace->load(['cluster', 'users', 'invitations']);

        // 获取工作空间成员
        $workspaceUsers = $workspace->users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'avatar' => $user->avatar(),
                'joined_at' => $user->pivot->created_at,
            ];
        });

        // 获取邀请列表
        $invitations = $workspace->invitations->map(function ($invitation) {
            return [
                'id' => $invitation->id,
                'email' => $invitation->email,
                'user_id' => $invitation->user_id,
                'invitee_identifier' => $invitation->invitee_identifier,
                'role' => $invitation->role,
                'created_at' => $invitation->created_at,
            ];
        });

        return Inertia::render('Workspaces/Show', [
            'workspace' => new WorkspaceResource($workspace),
            'workspaceUsers' => $workspaceUsers,
            'invitations' => $invitations,
            'owner' => [
                'id' => $workspace->user->id,
                'name' => $workspace->user->name,
                'email' => $workspace->user->email,
                'avatar' => $workspace->user->avatar(),
            ],
        ]);
    }

    /**
     * 显示编辑工作空间页面
     */
    public function edit(Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        return Inertia::render('Workspaces/Edit', [
            'workspace' => new WorkspaceResource($workspace->load('cluster')),
        ]);
    }

    /**
     * 更新工作空间
     */
    public function update(UpdateWorkspaceRequest $request, Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        try {
            $workspace->update($request->validated());

            return redirect()->route('workspaces.show', $workspace)
                ->with('success', '工作空间更新成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => '更新工作空间失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除工作空间
     */
    public function destroy(Workspace $workspace)
    {
        $this->authorize('delete', $workspace);

        try {
            // 如果是当前工作空间，清除用户的当前工作空间
            if (auth()->user()->current_workspace_id === $workspace->id) {
                auth()->user()->update(['current_workspace_id' => null]);
            }

            // 使用服务删除工作空间（包括 Kubernetes namespace）
            $success = $this->workspaceService->deleteWorkspace($workspace);

            if ($success) {
                return redirect()->route('workspaces.index')
                    ->with('success', '工作空间删除成功，相关资源正在后台清理中');
            } else {
                return redirect()->back()
                    ->withErrors(['general' => '删除工作空间失败']);
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => '删除工作空间失败：' . $e->getMessage()]);
        }
    }

    /**
     * 设置当前工作空间
     */
    public function setCurrent(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        try {
            auth()->user()->setCurrentWorkspace($workspace);

            return redirect()->back()
                ->with('success', '已切换到工作空间：' . $workspace->name);
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => '切换工作空间失败：' . $e->getMessage()]);
        }
    }

    /**
     * 移除工作空间成员
     */
    public function removeMember(Request $request, Workspace $workspace, User $user)
    {
        $this->authorize('update', $workspace);

        // 不能移除工作空间所有者
        if ($workspace->user_id === $user->id) {
            return back()->with('error', '无法移除工作空间所有者');
        }

        // 不能移除自己
        if ($request->user()->id === $user->id) {
            return back()->with('error', '无法移除自己');
        }

        // 从工作空间中移除用户
        $workspace->users()->detach($user);

        // 如果被移除的用户当前工作空间是这个，清除其当前工作空间
        if ($user->current_workspace_id === $workspace->id) {
            $user->update(['current_workspace_id' => null]);
        }

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '成员已从工作空间中移除');
    }


}
