<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;

class Document extends Model
{
    use HasFactory, Searchable, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'description',
        'team_id',
        'parent_id',
        'order',
    ];

    protected $casts = [
        'content' => 'array',
        'parent_id' => 'integer',
        'order' => 'float',
    ];

    /**
     * 默认隐藏的属性（content 字段很大，只在需要时获取）
     */
    protected $hidden = [
        'content',
    ];

    /**
     * 模型启动方法
     */
    protected static function boot()
    {
        parent::boot();

        // 创建文档时自动设置排序
        static::creating(function ($document) {
            if (is_null($document->order)) {
                $maxOrder = static::where('team_id', $document->team_id)
                    ->where('parent_id', $document->parent_id)
                    ->max('order');
                $document->order = ($maxOrder ?? 0.0) + 1.0;
            }
        });

        // 当文档被软删除时，同时软删除所有子级文档
        static::deleted(function ($document) {
            // 递归删除所有后代文档
            $document->deleteDescendants($document->isForceDeleting());

            // 如果不是强制删除，则重新排序同级文档以填补空缺
            if (! $document->isForceDeleting()) {
                static::where('team_id', $document->team_id)
                    ->where('parent_id', $document->parent_id)
                    ->where('order', '>', $document->order)
                    ->decrement('order');
            }
        });

        // 当文档被恢复时，可以选择是否恢复子级文档
        static::restored(function ($document) {
            // 这里可以根据业务需求决定是否自动恢复子级文档
            // $document->restoreDescendants();
        });

        // 监听文档更新事件，自动创建版本历史
        static::updated(function ($document) {
            // 避免在版本创建过程中触发无限循环
            if (! $document->isDirty('created_at') && ! $document->isDirty('updated_at')) {
                // 在测试环境中同步创建版本，在生产环境中异步创建
                if (app()->environment('testing')) {
                    $document->createVersionSnapshot('update');
                } else {
                    dispatch(function () use ($document) {
                        $document->createVersionSnapshot('update');
                    })->afterResponse();
                }
            }
        });

        // 监听文档创建事件，创建初始版本
        static::created(function ($document) {
            // 在测试环境中同步创建版本，在生产环境中异步创建
            if (app()->environment('testing')) {
                $document->createVersionSnapshot('create');
            } else {
                dispatch(function () use ($document) {
                    $document->createVersionSnapshot('create');
                })->afterResponse();
            }
        });
    }

    /**
     * 递归删除所有后代文档
     */
    private function deleteDescendants(bool $forceDelete = false)
    {
        // 使用批量查询获取所有子级，避免 N+1 查询
        $children = $this->children()->select('id', 'title', 'parent_id', 'team_id')->get();

        foreach ($children as $child) {
            if ($forceDelete) {
                $child->forceDelete();
            } else {
                $child->delete();
            }
        }
    }

    /**
     * 递归恢复所有后代文档
     */
    public function restoreDescendants()
    {
        // 使用批量查询获取所有已删除的子级，避免 N+1 查询
        $children = $this->children()->onlyTrashed()->select('id', 'title', 'parent_id', 'team_id')->get();

        foreach ($children as $child) {
            $child->restore();
            $child->restoreDescendants();
        }
    }

    /**
     * 获取拥有此文档的团队
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 父级文档关联
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Document::class, 'parent_id');
    }

    /**
     * 子级文档关联
     */
    public function children(): HasMany
    {
        return $this->hasMany(Document::class, 'parent_id')->orderBy('order')->orderBy('created_at');
    }

    /**
     * 文档分享关联
     */
    public function shares(): HasMany
    {
        return $this->hasMany(DocumentShare::class);
    }

    /**
     * 文档版本历史关联
     */
    public function versions(): HasMany
    {
        return $this->hasMany(DocumentVersion::class)->orderBy('created_at', 'desc');
    }

    /**
     * 获取有效的分享
     */
    public function activeShares(): HasMany
    {
        return $this->shares()->valid();
    }

    /**
     * 递归获取所有子级文档
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * 递归获取所有子级文档（用于 API 返回）
     */
    public function allChildren(): HasMany
    {
        return $this->hasMany(Document::class, 'parent_id')
            ->with('allChildren')
            ->orderBy('order')
            ->orderBy('created_at');
    }

    /**
     * 获取同级文档中的下一个排序值
     */
    public function getNextOrderValue(): float
    {
        $maxOrder = static::where('team_id', $this->team_id)
            ->where('parent_id', $this->parent_id)
            ->max('order');

        return ($maxOrder ?? 0.0) + 1.0;
    }

    /**
     * 显式获取包含 content 的文档
     */
    public function withContent(): Document
    {
        return $this->makeVisible(['content']);
    }

    /**
     * 作用域：包含 content 字段
     */
    public function scopeWithContent(Builder $query): Builder
    {
        return $query->addSelect('content');
    }

    /**
     * 获取两个排序值之间的中间值
     */
    public function getOrderBetween(?float $before, ?float $after): float
    {
        // 如果没有前一个，使用后一个的一半
        if ($before === null) {
            return ($after ?? 1.0) / 2.0;
        }

        // 如果没有后一个，使用前一个加1
        if ($after === null) {
            return $before + 1.0;
        }

        // 计算中间值，保留足够精度
        $middle = ($before + $after) / 2.0;

        // 检查精度是否足够
        $minDiff = 0.000001; // 设置最小差值为百万分之一
        $range = $after - $before;

        if ($range < $minDiff) {
            // 如果范围太小，触发重新排序
            $this->rebalanceOrdersForParent($this->parent_id ?? null);

            // 重新排序后，返回一个安全的中间值
            return ($before + $after) / 2.0;
        }

        return $middle;
    }

    /**
     * 重新平衡指定父级下的文档排序值
     */
    private function rebalanceOrdersForParent(?int $parentId): void
    {
        $siblings = static::where('team_id', $this->team_id)
            ->where('parent_id', $parentId)
            ->orderBy('order')
            ->orderBy('created_at')
            ->get();

        if ($siblings->isEmpty()) {
            return;
        }

        $step = 10.0; // 使用更大的步长，为后续插入留出空间
        foreach ($siblings as $index => $sibling) {
            $newOrder = ($index + 1) * $step;
            if ($sibling->order !== $newOrder) {
                $sibling->order = $newOrder;
                $sibling->saveQuietly();
            }
        }
    }

    /**
     * 更新文档排序和/或父级
     *
     * @param  float  $newOrder  新的排序位置
     * @param  int|null  $newParentId  新的父级ID
     */
    public function updateOrder(float $newOrder, ?int $newParentId): void
    {
        if ($newOrder === $this->order && $newParentId === $this->parent_id) {
            return;
        }

        DB::transaction(function () use ($newOrder, $newParentId) {
            // 直接更新当前文档的排序和父级，不需要移动其他文档
            // 使用 float 的优势是可以插入到任意两个值之间
            $this->order = $newOrder;
            $this->parent_id = $newParentId;
            $this->saveQuietly();
        });
    }

    /**
     * 获取所有祖先文档
     */
    public function ancestors()
    {
        $ancestors = collect();
        $current = $this->parent;
        $visited = []; // 防止无限循环

        while ($current && ! in_array($current->id, $visited)) {
            $visited[] = $current->id;
            $ancestors->push($current);
            // 预加载父级关系，避免 N+1 查询
            $current = $current->parent()->select('id', 'title', 'parent_id', 'team_id')->first();
        }

        return $ancestors->reverse();
    }

    /**
     * 获取根级文档
     */
    public function root(): Document
    {
        $current = $this;
        $visited = []; // 防止无限循环

        while ($current->parent && ! in_array($current->id, $visited)) {
            $visited[] = $current->id;
            // 使用 select 优化查询，只获取必要字段
            $current = $current->parent()->select('id', 'title', 'parent_id', 'team_id')->first();
        }

        return $current;
    }

    /**
     * 检查是否为根级文档
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * 检查是否为叶子节点（没有子级）
     */
    public function isLeaf(): bool
    {
        // 使用 exists() 而不是 count()，性能更好
        return ! $this->children()->exists();
    }

    /**
     * 获取文档层级深度
     */
    public function getDepth(): int
    {
        return $this->ancestors()->count();
    }

    /**
     * 检查是否为指定文档的子级
     */
    public function isChildOf(Document $document): bool
    {
        return $this->parent_id === $document->id;
    }

    /**
     * 检查是否为指定文档的后代
     */
    public function isDescendantOf(Document $document): bool
    {
        // 优化：使用递归 CTE 查询，避免 N+1 查询
        $query = '
            WITH RECURSIVE document_ancestors AS (
                -- 基础查询：选择当前文档
                SELECT id, parent_id
                FROM documents
                WHERE id = ? AND deleted_at IS NULL

                UNION ALL

                -- 递归查询：选择父级文档
                SELECT d.id, d.parent_id
                FROM documents d
                INNER JOIN document_ancestors da ON d.id = da.parent_id
                WHERE d.deleted_at IS NULL
            )
            SELECT COUNT(*) as count FROM document_ancestors WHERE id = ?
        ';

        $result = DB::selectOne($query, [$this->id, $document->id]);

        return $result->count > 0;
    }

    /**
     * 作用域：仅获取根级文档
     */
    public function scopeRoots(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 作用域：获取指定父级的子文档
     */
    public function scopeChildrenOf(Builder $query, ?int $parentId): Builder
    {
        return $query->where('parent_id', $parentId);
    }

    /**
     * 作用域：获取叶子节点
     */
    public function scopeLeaves(Builder $query): Builder
    {
        return $query->whereDoesntHave('children');
    }

    /**
     * 作用域：优化的预加载关系
     */
    public function scopeWithOptimizedRelations(Builder $query): Builder
    {
        return $query->with([
            'parent:id,title,parent_id',
            'children:id,title,parent_id,order',
        ]);
    }

    /**
     * 作用域：按团队和排序获取
     */
    public function scopeByTeamOrdered(Builder $query, int $teamId, string $orderBy = 'order', string $direction = 'asc'): Builder
    {
        return $query->where('team_id', $teamId)
            ->orderBy($orderBy, $direction);
    }

    /**
     * 作用域：获取团队的根级文档
     */
    public function scopeTeamRoots(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId)
            ->whereNull('parent_id')
            ->orderBy('order')
            ->orderBy('created_at');
    }

    /**
     * 获取 URL
     */
    public function getUrl(): ?string
    {
        return route('document.show', $this->id);
    }

    public function getPlainTextContent(): string
    {
        // withContent
        $this->withContent();
        if (empty($this->content) || ! is_array($this->content)) {
            return '';
        }

        $textParts = [];

        // 递归提取所有文本内容
        $this->extractAllText($this->content, $textParts);

        return implode(' ', array_filter($textParts));
    }

    /**
     * 递归提取数组中所有的文本内容
     */
    private function extractAllText($data, array &$textParts): string
    {
        if (! is_array($data)) {
            return '';
        }

        foreach ($data as $key => $value) {
            if ($key === 'text' && is_string($value)) {
                // 找到 text 字段，直接添加
                $textParts[] = $value;
            } elseif (is_array($value)) {
                // 递归处理数组
                $this->extractAllText($value, $textParts);
            }
        }

        return implode(' ', array_filter($textParts));
    }

    /**
     * 高效获取所有后代文档及自身
     */
    public function getAllDescendantsAndSelf(): \Illuminate\Database\Eloquent\Collection
    {
        // 使用递归 CTE 查询一次性获取所有后代文档，包含 content 字段
        $query = '
            WITH RECURSIVE document_descendants AS (
                -- 基础查询：选择根文档
                SELECT id, title, description, content, parent_id, team_id, "order", created_at, updated_at, deleted_at
                FROM documents
                WHERE id = ? AND deleted_at IS NULL

                UNION ALL

                -- 递归查询：选择子文档
                SELECT d.id, d.title, d.description, d.content, d.parent_id, d.team_id, d."order", d.created_at, d.updated_at, d.deleted_at
                FROM documents d
                INNER JOIN document_descendants dd ON d.parent_id = dd.id
                WHERE d.deleted_at IS NULL AND d.team_id = ?
            )
            SELECT * FROM document_descendants
            ORDER BY "order", created_at
        ';

        $results = DB::select($query, [$this->id, $this->team_id]);

        // 将查询结果转换为 Document 模型实例的 Eloquent Collection
        $documents = collect($results)->map(function ($row) {
            $document = new static;
            $document->setRawAttributes((array) $row, true);
            $document->exists = true;
            // 确保 content 字段可见
            $document->makeVisible(['content']);

            return $document;
        });

        // 返回 Eloquent Collection
        return new \Illuminate\Database\Eloquent\Collection($documents->all());
    }

    /**
     * 获取面包屑导航
     */
    public function getBreadcrumbs(): array
    {
        // 使用递归 CTE 查询一次性获取所有祖先，避免 N+1 查询
        $query = '
            WITH RECURSIVE document_ancestors AS (
                -- 基础查询：选择当前文档
                SELECT id, title, parent_id, 0 as level
                FROM documents
                WHERE id = ? AND deleted_at IS NULL

                UNION ALL

                -- 递归查询：选择父级文档
                SELECT d.id, d.title, d.parent_id, da.level + 1 as level
                FROM documents d
                INNER JOIN document_ancestors da ON d.id = da.parent_id
                WHERE d.deleted_at IS NULL
            )
            SELECT id, title FROM document_ancestors
            WHERE level > 0
            ORDER BY level DESC
        ';

        $ancestors = DB::select($query, [$this->id]);

        $breadcrumbs = [];
        foreach ($ancestors as $ancestor) {
            $breadcrumbs[] = [
                'id' => $ancestor->id,
                'title' => $ancestor->title ?? '未命名文档',
                'url' => route('document.show', $ancestor->id),
            ];
        }

        // 添加当前文档
        $breadcrumbs[] = [
            'id' => $this->id,
            'title' => $this->title ?? '未命名文档',
            'url' => $this->getUrl(),
        ];

        return $breadcrumbs;
    }

    /**
     * 判断用户是否可以编辑文档
     */
    public function canUserEditDocument(User $user): bool
    {
        return $user->inTeamId($this->team_id);
    }

    /**
     * 创建版本快照
     */
    public function createVersionSnapshot(string $changeType = 'update', ?string $changeSummary = null, ?User $user = null): DocumentVersion
    {
        // 如果没有提供用户，尝试从当前认证用户获取
        if (! $user && auth()->check()) {
            $user = auth()->user();
        }

        return $this->versions()->create([
            'title' => $this->title,
            'description' => $this->description,
            'content' => $this->content,
            'created_by' => $user?->id,
            'change_type' => $changeType,
            'change_summary' => $changeSummary,
            'metadata' => [],
        ]);
    }

    /**
     * 恢复到指定版本
     */
    public function restoreToVersion(DocumentVersion $version, ?User $user = null): bool
    {
        if ($version->document_id !== $this->id) {
            return false;
        }

        // 在恢复之前先创建当前状态的快照
        $this->createVersionSnapshot('restore_backup', "恢复前的备份（恢复到版本 #{$version->id}）", $user);

        // 更新文档内容
        $this->update([
            'title' => $version->title,
            'description' => $version->description,
            'content' => $version->content,
        ]);

        // 创建恢复操作的版本记录
        $this->createVersionSnapshot('restore', "恢复到版本 #{$version->id}", $user);

        return true;
    }

    /**
     * 获取最新版本
     */
    public function getLatestVersion(): ?DocumentVersion
    {
        return $this->versions()->first();
    }

    /**
     * 清理旧版本（保留最近的指定数量）
     */
    public function cleanupOldVersions(int $keepCount = 50): int
    {
        $totalVersions = $this->versions()->count();

        if ($totalVersions <= $keepCount) {
            return 0;
        }

        $versionsToDelete = $this->versions()
            ->skip($keepCount)
            ->pluck('id');

        return DocumentVersion::whereIn('id', $versionsToDelete)->delete();
    }
}
