<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AIFunctionCall extends Model
{
    use HasFactory;

    protected $table = 'ai_function_calls';

    protected $fillable = [
        'ai_message_id',
        'name',
        'arguments',
        'result',
        'success',
        'error',
    ];

    protected $casts = [
        'arguments' => 'json',
        'result' => 'json',
        'success' => 'boolean',
    ];

    /**
     * 获取函数调用所属的消息
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(AIMessage::class, 'ai_message_id');
    }
}
