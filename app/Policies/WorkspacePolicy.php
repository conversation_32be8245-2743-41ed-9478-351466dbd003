<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Workspace;

class WorkspacePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // 用户可以查看自己的工作空间列表
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Workspace $workspace): bool
    {
        return $workspace->canAccess($user);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // 认证用户都可以创建工作空间
    }

    /**
     * Determine whether the user can update the model.
     * 只有工作空间所有者可以更新工作空间设置
     */
    public function update(User $user, Workspace $workspace): bool
    {
        return $workspace->isOwner($user);
    }

    /**
     * Determine whether the user can delete the model.
     * 只有工作空间所有者可以删除工作空间
     */
    public function delete(User $user, Workspace $workspace): bool
    {
        return $workspace->isOwner($user);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Workspace $workspace): bool
    {
        return $workspace->isOwner($user);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Workspace $workspace): bool
    {
        return $workspace->isOwner($user);
    }
}
