<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Workspace;
use App\Models\WorkspaceInvitation;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class WorkspaceInvitationController extends Controller
{
    /**
     * 显示邀请成员表单
     */
    public function inviteForm(Workspace $workspace): Response
    {
        $this->authorize('update', $workspace);

        return Inertia::render('Workspaces/Invite', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 邀请新成员加入工作空间
     */
    public function invite(Request $request, Workspace $workspace): RedirectResponse
    {
        $this->authorize('update', $workspace);

        $this->validate($request, [
            'identifier' => [
                'required',
                'string',
            ],
            'role' => ['nullable', 'string'],
        ]);

        $identifier = $request->identifier;
        $targetUser = null;
        $email = null;

        // 尝试通过不同方式查找用户
        if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
            // 如果是邮箱格式
            $email = $identifier;
            $targetUser = User::where('email', $identifier)->first();
        } else {
            // 如果是用户名或ID
            if (is_numeric($identifier)) {
                $targetUser = User::find($identifier);
            } else {
                $targetUser = User::where('name', $identifier)->first();
            }
        }

        // 不能邀请自己
        if ($targetUser && $targetUser->id === $request->user()->id) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['identifier' => '不能邀请自己'])
                ->withInput();
        }

        // 不能邀请所有者
        if ($targetUser && $targetUser->id === $workspace->user_id) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['identifier' => '不能邀请工作空间所有者'])
                ->withInput();
        }

        // 检查用户是否已是工作空间成员
        if ($targetUser && $workspace->hasUser($targetUser)) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['identifier' => '该用户已是工作空间成员'])
                ->withInput();
        }

        // 检查是否已有相同的邀请
        $existingInvitation = $workspace->invitations()
            ->where(function ($query) use ($targetUser, $email) {
                if ($targetUser) {
                    $query->where('user_id', $targetUser->id);
                } else {
                    $query->where('email', $email);
                }
            })
            ->first();

        if ($existingInvitation) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['identifier' => '已向该用户发送过邀请'])
                ->withInput();
        }

        // 创建邀请
        $invitationData = [
            'token' => Str::random(40),
            'role' => $request->role,
        ];

        if ($targetUser) {
            $invitationData['user_id'] = $targetUser->id;
        } else {
            $invitationData['email'] = $email;
        }

        $invitation = $workspace->invitations()->create($invitationData);

        // TODO: 发送邀请邮件
        // Mail::to($request->email)->send(new WorkspaceInvitation($invitation));

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '邀请已发送');
    }

    /**
     * 接受工作空间邀请
     */
    public function acceptInvitation(string $token): RedirectResponse
    {
        $invitation = WorkspaceInvitation::where('token', $token)->firstOrFail();
        $workspace = $invitation->workspace;
        $user = Auth::user();

        // 检查邀请是否属于当前用户
        $canAcceptInvitation = false;

        if ($invitation->user_id) {
            // 通过用户ID邀请
            $canAcceptInvitation = $invitation->user_id === $user->id;
        } elseif ($invitation->email) {
            // 通过邮箱邀请
            $canAcceptInvitation = $invitation->email === $user->email;
        }

        if (!$canAcceptInvitation) {
            abort(403, '此邀请不属于您');
        }

        // 将用户添加到工作空间
        $workspace->users()->attach($user);

        // 如果用户没有当前工作空间，设置为当前工作空间
        if (!$user->current_workspace_id) {
            $user->setCurrentWorkspace($workspace);
        }

        // TODO: 分配角色（如果指定了角色）
        // if ($invitation->role) {
        //     $user->assignRole($invitation->role);
        // }

        // 删除邀请
        $invitation->delete();

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '您已成功加入工作空间');
    }

    /**
     * 显示用户的邀请列表
     */
    public function invitations(): Response
    {
        $user = Auth::user();
        $invitations = WorkspaceInvitation::where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('email', $user->email);
            })
            ->with('workspace')
            ->get();

        return Inertia::render('Workspaces/Invitations', [
            'invitations' => $invitations,
        ]);
    }

    /**
     * 拒绝工作空间邀请
     */
    public function declineInvitation(WorkspaceInvitation $invitation): RedirectResponse
    {
        $user = Auth::user();

        // 检查邀请是否属于当前用户
        $canDeclineInvitation = false;

        if ($invitation->user_id) {
            // 通过用户ID邀请
            $canDeclineInvitation = $invitation->user_id === $user->id;
        } elseif ($invitation->email) {
            // 通过邮箱邀请
            $canDeclineInvitation = $invitation->email === $user->email;
        }

        if (!$canDeclineInvitation) {
            abort(403, '无权操作此邀请');
        }

        $invitation->delete();

        return redirect()->route('workspace-invitations.index')
            ->with('success', '已拒绝工作空间邀请');
    }

    /**
     * 取消工作空间邀请（工作空间管理员操作）
     */
    public function cancelInvitation(WorkspaceInvitation $invitation): RedirectResponse
    {
        $workspace = $invitation->workspace;
        $this->authorize('update', $workspace);

        $invitation->delete();

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '邀请已取消');
    }
}
