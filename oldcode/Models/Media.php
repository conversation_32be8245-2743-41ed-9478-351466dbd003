<?php

namespace App\Models;

use App\Events\MediaUploaded;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Media extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'media';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'team_id',
        'file_id',
        'title',
        'alt_text',
        'description',
        'mime_type',
        'size',
        'face_detection_data',
        'ocr_data',
        'width',
        'height',
        'metadata',
        'ulid',
    ];

    /**
     * 类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'face_detection_data' => 'array',
        'ocr_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * 获取所属团队
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取文件记录（实际的文件信息）
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(File::class);
    }

    /**
     * 获取指定尺寸的缩略图URL
     *
     * @param  string  $size  缩略图尺寸 (small, medium, large)
     * @return string|null 缩略图URL
     */
    public function getThumbnailUrl(string $size = 'medium'): ?string
    {
        if (! $this->file || empty($this->file->thumbnails)) {
            return null;
        }

        return $this->file->thumbnails[$size] ?? null;
    }

    /**
     * 获取文件类型分类
     *
     * @return string 文件类型 (image, document, other)
     */
    public function getFileType(): string
    {
        $allowedTypes = config('media.allowed_types', []);

        foreach ($allowedTypes as $type => $mimeTypes) {
            if (in_array($this->mime_type, $mimeTypes)) {
                return $type;
            }
        }

        // 特殊处理图片类型(兼容前缀匹配)
        if (str_starts_with($this->mime_type, 'image/')) {
            return 'image';
        }

        return 'other';
    }

    /**
     * 获取文件的临时下载URL
     *
     * @param  int  $minutes  有效期（分钟）
     * @return string|null 临时下载URL
     */
    public function getTemporaryUrl(int $minutes = 10): ?string
    {
        if (! $this->file || ! $this->file->path) {
            return null;
        }

        try {
            $disk = config('media.storage.disk', 's3');
            $path = $this->file->path;

            // 检查文件是否存在
            if (! Storage::disk($disk)->exists($path)) {
                return null;
            }

            // 生成带签名的临时URL
            return Storage::disk($disk)->temporaryUrl(
                $path,
                now()->addMinutes($minutes)
            );
        } catch (\Exception $e) {
            \Log::error('Failed to generate temporary URL for file', [
                'media_id' => $this->id,
                'file_id' => $this->file_id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 检查文件类型是否被允许
     *
     * @param  string  $mimeType  文件MIME类型
     * @return bool 是否允许
     */
    public static function isAllowedMimeType(string $mimeType): bool
    {
        $allowedTypes = config('media.allowed_types', []);

        foreach ($allowedTypes as $mimeTypes) {
            if (in_array($mimeType, $mimeTypes)) {
                return true;
            }
        }

        // 特殊处理图片类型(兼容前缀匹配)
        if (str_starts_with($mimeType, 'image/')) {
            return true;
        }

        return false;
    }

    /**
     * 获取所有允许的MIME类型列表
     *
     * @return array<string> MIME类型列表
     */
    public static function getAllowedMimeTypes(): array
    {
        $allowedTypes = config('media.allowed_types', []);
        $mimeTypes = [];

        foreach ($allowedTypes as $type => $types) {
            $mimeTypes = array_merge($mimeTypes, $types);
        }

        return $mimeTypes;
    }

    /**
     * 获取特定类型的MIME类型列表
     *
     * @param  string  $type  文件类型 (image, document)
     * @return array<string> MIME类型列表
     */
    public static function getMimeTypesByType(string $type): array
    {
        return config("media.allowed_types.{$type}", []);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function (self $media) {
            if (empty($media->ulid)) {
                $media->ulid = Str::ulid()->toString();
            }
        });

        static::created(function (self $media) {
            MediaUploaded::dispatch($media);
        });
    }
}
