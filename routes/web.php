<?php

use App\Http\Controllers\ApplicationController;
use App\Http\Controllers\ApplyController;
use App\Http\Controllers\BalanceController;
use App\Http\Controllers\ConfigMapController;
use App\Http\Controllers\DeploymentController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\HorizontalPodAutoscalerController;
use App\Http\Controllers\IngressController;
use App\Http\Controllers\OAuthController;
use App\Http\Controllers\OverviewController;
use App\Http\Controllers\PersonalAccessTokenController;
use App\Http\Controllers\PodController;
use App\Http\Controllers\SecretController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\Settings\ProfileController;
use App\Http\Controllers\StatefulSetController;
use App\Http\Controllers\StorageController;
use App\Http\Controllers\UserSimulationController;
use App\Http\Controllers\WorkspaceController;
use App\Http\Middleware\MustInWorkspace;
use App\Http\Middleware\WorkspaceMustActive;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/* 首页 */
Route::get('/', [HomeController::class, 'index'])->name('home');

/* 法律页面 */
Route::inertia('legals/tos', 'Legals/TOS')->name('legals.tos');
Route::inertia('legals/privacy-policy', 'Legals/PrivacyPolicy')->name('legals.privacy-policy');

/* 控制台 */
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('workspaces', WorkspaceController::class);
    Route::post('/workspaces/{workspace}/set-current', [WorkspaceController::class, 'setCurrent'])->name('workspaces.set-current');

    // 工作空间成员管理
    Route::get('/workspaces/{workspace}/members', [WorkspaceController::class, 'members'])->name('workspaces.members');
    Route::delete('/workspaces/{workspace}/members/{user}', [WorkspaceController::class, 'removeMember'])->name('workspaces.remove-member');

    // 工作空间邀请管理
    Route::get('/workspaces/{workspace}/invite', [\App\Http\Controllers\WorkspaceInvitationController::class, 'inviteForm'])->name('workspaces.invite');
    Route::post('/workspaces/{workspace}/invite', [\App\Http\Controllers\WorkspaceInvitationController::class, 'invite'])->name('workspaces.invite.store');
    Route::get('/workspace-invitations', [\App\Http\Controllers\WorkspaceInvitationController::class, 'invitations'])->name('workspace-invitations.index');
    Route::get('/workspace-invitations/{token}/accept', [\App\Http\Controllers\WorkspaceInvitationController::class, 'acceptInvitation'])->name('workspace-invitations.accept');
    Route::delete('/workspace-invitations/{invitation}/decline', [\App\Http\Controllers\WorkspaceInvitationController::class, 'declineInvitation'])->name('workspace-invitations.decline');
    Route::delete('/workspace-invitations/{invitation}/cancel', [\App\Http\Controllers\WorkspaceInvitationController::class, 'cancelInvitation'])->name('workspace-invitations.cancel');

    // 需要在工作空间中的路由
    Route::middleware([MustInWorkspace::class, WorkspaceMustActive::class])->group(function () {
        Route::get('/dashboard', OverviewController::class)->name('dashboard');

        // 应用管理统一页面
        Route::resource('applications', ApplicationController::class)->only(['index', 'create']);

        // Deployment 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('deployments', DeploymentController::class)->only(['index', 'create', 'show', 'edit']);

        // StatefulSet 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('statefulsets', StatefulSetController::class)->only(['index', 'create', 'show', 'edit']);

        // Secret 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('secrets', SecretController::class)->only(['index', 'create', 'show', 'edit']);

        // ConfigMap 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('configmaps', ConfigMapController::class)->only(['index', 'create', 'show', 'edit']);

        // Storage 管理路由 - 只有 index, create 方法
        Route::resource('storages', StorageController::class)->only(['index', 'create']);

        // Service 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('services', ServiceController::class)->only(['index', 'create', 'show', 'edit']);

        // Ingress 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('ingresses', IngressController::class)->only(['index', 'create', 'show', 'edit']);

        // Pod 管理路由 - 只有 index, show 方法
        Route::resource('pods', PodController::class)->only(['index', 'show']);
        Route::get('pods/{name}/logs', [PodController::class, 'logs'])->name('pods.logs');
        Route::get('pods/{name}/terminal', [PodController::class, 'terminal'])->name('pods.terminal');

        // HorizontalPodAutoscaler 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('hpas', HorizontalPodAutoscalerController::class)->only(['index', 'create', 'show', 'edit']);

        // Apply YAML 资源应用
        Route::get('/apply', [ApplyController::class, 'index'])->name('apply.index');
        Route::post('/apply/get-manifest', [ApplyController::class, 'getManifest'])->name('apply.get-manifest');

        // Settings
        Route::get('/settings/profile', [ProfileController::class, 'edit'])->name('settings.profile.edit');
        Route::patch('/settings/profile', [ProfileController::class, 'update'])->name('settings.profile.update');

        // 桌面
        Route::get('/desktop', [\App\Http\Controllers\DesktopController::class, 'index'])->name('desktop.index');
        Route::post('/desktop/wallpaper', [\App\Http\Controllers\DesktopController::class, 'uploadWallpaper'])->name('desktop.wallpaper');
        Route::post('/desktop/icon-order', [\App\Http\Controllers\DesktopController::class, 'saveIconOrder'])->name('desktop.icon-order');
        Route::match(['delete'], '/desktop/wallpaper', [\App\Http\Controllers\DesktopController::class, 'removeWallpaper'])->name('desktop.wallpaper.delete');

        // Webhook 管理路由
        Route::resource('webhooks', \App\Http\Controllers\WebhookController::class)->parameters([
            'webhooks' => 'webhook',
        ]);
        Route::post('/webhooks/{webhook}/test', [\App\Http\Controllers\WebhookController::class, 'test'])->name('webhooks.test');
        Route::post('/webhooks/{webhook}/toggle', [\App\Http\Controllers\WebhookController::class, 'toggle'])->name('webhooks.toggle');
        Route::post('/webhooks/{webhook}/regenerate-secret', [\App\Http\Controllers\WebhookController::class, 'regenerateSecret'])->name('webhooks.regenerate-secret');
        Route::get('/webhooks/{webhook}/deliveries/{delivery}', [\App\Http\Controllers\WebhookController::class, 'showDelivery'])->name('webhooks.deliveries.show');
    });

    // SDK测试路由
    Route::get('/tests/publisher', function () {
        return Inertia::render('Tests/Publisher');
    })->name('tests.publisher');
    Route::get('/tests/subscriber', function () {
        return Inertia::render('Tests/Subscriber');
    })->name('tests.subscriber');
    Route::get('/tests/external', function () {
        return response()->file(public_path('external-test.html'));
    })->name('tests.external');

    // Balance
    Route::get('/balance', [BalanceController::class, 'index'])->name('balance.index');
    Route::post('/balance/top-up', [BalanceController::class, 'topUp'])->name('balance.top-up');
    Route::post('/balance/redeem', [BalanceController::class, 'redeem'])->name('balance.redeem');

    // OAuth 2.0 Token & Client 管理页面
    Route::get('/openid/tokens', [PersonalAccessTokenController::class, 'index'])->name('oauth.tokens.index');
    Route::post('/openid/tokens', [PersonalAccessTokenController::class, 'store'])->name('oauth.tokens.store');
    Route::delete('/openid/tokens/{token}', [PersonalAccessTokenController::class, 'destroy'])->name('oauth.tokens.destroy');
    Route::delete('/openid/clients/{client}/revoke', [PersonalAccessTokenController::class, 'revokeClientAuthorization'])->name('oauth.clients.revoke');

    Route::get('/openid/clients', [OAuthController::class, 'clients'])->name('oauth.clients.index');
    Route::post('/openid/clients', [OAuthController::class, 'store'])->name('oauth.clients.store');
    Route::put('/openid/clients/{client}', [OAuthController::class, 'update'])->name('oauth.clients.update');
    Route::delete('/openid/clients/{client}', [OAuthController::class, 'destroy'])->name('oauth.clients.destroy');

    // 模拟用户登录路由
    Route::get('/user/simulate-login/{token}', [UserSimulationController::class, 'login'])->name('user.simulate.login');

});

Route::passkeys();
