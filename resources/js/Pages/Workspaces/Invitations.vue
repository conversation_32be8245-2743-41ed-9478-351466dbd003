<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="我的邀请" />

        <div class="container p-4">
            <Card class="mx-auto max-w-4xl">
                <CardHeader>
                    <CardTitle>工作空间邀请</CardTitle>
                    <CardDescription>您收到的工作空间邀请</CardDescription>
                </CardHeader>
                <CardContent>
                    <div v-if="invitations.length === 0" class="text-center py-12">
                        <div class="text-gray-500 dark:text-gray-400">
                            <Mail class="mx-auto h-12 w-12 mb-4" />
                            <p class="text-lg font-medium">暂无邀请</p>
                            <p class="text-sm">您目前没有收到任何工作空间邀请</p>
                        </div>
                    </div>
                    <div v-else class="space-y-4">
                        <div
                            v-for="invitation in invitations"
                            :key="invitation.id"
                            class="border rounded-lg p-4 space-y-4"
                        >
                            <div class="flex items-start justify-between">
                                <div class="space-y-2">
                                    <h3 class="font-semibold text-lg">{{ invitation.workspace.name }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        邀请您加入工作空间
                                    </p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                        <span>邀请邮箱：{{ invitation.email }}</span>
                                        <span v-if="invitation.role">角色：{{ invitation.role }}</span>
                                        <span>邀请时间：{{ formatDate(invitation.created_at) }}</span>
                                    </div>
                                </div>
                                <Badge variant="secondary">待处理</Badge>
                            </div>
                            
                            <div class="flex items-center justify-end space-x-3">
                                <Button
                                    variant="outline"
                                    @click="declineInvitation(invitation)"
                                >
                                    拒绝
                                </Button>
                                <Button
                                    @click="acceptInvitation(invitation)"
                                >
                                    接受邀请
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Workspace } from '@/types/workspace';
import { Head, router } from '@inertiajs/vue3';
import { Mail } from 'lucide-vue-next';

interface WorkspaceInvitation {
    id: number;
    email: string;
    role?: string;
    created_at: string;
    token: string;
    workspace: Workspace;
}

const props = defineProps<{
    invitations: WorkspaceInvitation[];
}>();

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '工作空间', href: route('workspaces.index') },
    { title: '我的邀请', href: route('workspace-invitations.index') },
] as { title: string; href: string }[];

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const acceptInvitation = (invitation: WorkspaceInvitation) => {
    router.get(route('workspace-invitations.accept', invitation.token));
};

const declineInvitation = (invitation: WorkspaceInvitation) => {
    if (confirm(`确定要拒绝加入工作空间 "${invitation.workspace.name}" 的邀请吗？`)) {
        router.delete(route('workspace-invitations.decline', invitation.id));
    }
};
</script>
