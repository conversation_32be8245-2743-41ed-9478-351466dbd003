<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="团队邀请" />

        <div class="container space-y-6 p-4">
            <Card class="overflow-hidden">
                <CardHeader>
                    <CardTitle>团队邀请</CardTitle>
                    <CardDescription>查看并管理您收到的团队邀请</CardDescription>
                </CardHeader>

                <CardContent>
                    <div
                        v-if="page.props?.flash?.success"
                        class="mb-6 rounded-md border border-green-200 bg-green-50 p-4 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
                    >
                        <div class="flex items-center">
                            <CheckCircle class="mr-2 h-4 w-4" />
                            <div class="font-medium">成功</div>
                        </div>
                        <div class="mt-2 text-sm">{{ page.props.flash.success }}</div>
                    </div>

                    <div
                        v-if="page.props?.flash?.error"
                        class="mb-6 rounded-md border border-red-200 bg-red-50 p-4 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
                    >
                        <div class="flex items-center">
                            <AlertCircle class="mr-2 h-4 w-4" />
                            <div class="font-medium">错误</div>
                        </div>
                        <div class="mt-2 text-sm">{{ page.props.flash.error }}</div>
                    </div>

                    <div v-if="invitations.length > 0">
                        <div class="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>团队</TableHead>
                                        <TableHead>角色</TableHead>
                                        <TableHead>邀请时间</TableHead>
                                        <TableHead class="text-right">操作</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <TableRow v-for="invitation in invitations" :key="invitation.id">
                                        <TableCell>
                                            <div class="font-medium">{{ invitation.team.display_name || invitation.team.name }}</div>
                                        </TableCell>
                                        <TableCell>
                                            <span
                                                class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                                            >
                                                {{ invitation.role }}
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            {{ formatDate(invitation.created_at) }}
                                        </TableCell>
                                        <TableCell class="text-right">
                                            <div class="flex justify-end space-x-2">
                                                <Button variant="default" size="sm" @click="acceptInvitation(invitation)">
                                                    <CheckIcon class="mr-1 h-4 w-4" />
                                                    接受
                                                </Button>
                                                <Button variant="outline" size="sm" @click="declineInvitation(invitation)">
                                                    <XIcon class="mr-1 h-4 w-4" />
                                                    拒绝
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                    <div
                        v-else
                        class="mt-4 rounded-md border border-blue-200 bg-blue-50 p-4 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
                    >
                        <div class="flex items-center">
                            <Info class="mr-2 h-4 w-4" />
                            <div class="font-medium">没有邀请</div>
                        </div>
                        <div class="mt-2 text-sm">您目前没有待处理的团队邀请。</div>
                    </div>

                    <div class="mt-6">
                        <Button variant="outline" @click="router.visit(route('teams.index'))">
                            <ArrowLeft class="mr-1 h-4 w-4" />
                            返回团队列表
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Team } from '@/types/team';
import { Head, router, usePage } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, CheckCircle, CheckIcon, Info, XIcon } from 'lucide-vue-next';

const page = usePage();

interface TeamInvitation {
    id: number;
    team_id: number;
    email: string;
    role: string;
    token: string;
    created_at: string;
    updated_at: string;
    team: Team;
}

defineProps<{
    invitations: TeamInvitation[];
}>();

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
    { title: '团队邀请', href: route('teams.invitations') },
] as { title: string; href: string }[];

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
};

const acceptInvitation = (invitation: TeamInvitation) => {
    router.get(route('teams.accept-invitation', invitation.token));
};

const declineInvitation = (invitation: TeamInvitation) => {
    if (confirm(`确定要拒绝来自 ${invitation.team.display_name || invitation.team.name} 的邀请吗？`)) {
        router.delete(route('teams.invitations.decline', invitation.id));
    }
};
</script>
