<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Models\TeamInvitation;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TeamController extends Controller
{
    /**
     * Display a listing of the teams.
     *
     * @return Response
     */
    public function index()
    {
        $user = Auth::user();
        $teams = $user->teamsWithRole();

        $invitationCount = TeamInvitation::where('email', $user->email)
            ->with('team')
            ->count();

        return Inertia::render('Teams/Index', [
            'teams' => $teams,
            'currentTeam' => $user->getTeam(),
            'invitationCount' => $invitationCount,
        ]);
    }

    /**
     * Show the form for creating a new team.
     *
     * @return Response
     */
    public function create()
    {
        return Inertia::render('Teams/Create');
    }

    /**
     * Store a newly created team in storage.
     *
     * @return RedirectResponse
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => ['required', 'string', 'max:255'],
            'display_name' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        $user = Auth::user();

        $team = $user->ownedTeams()->create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'owner_id' => $user->id,
        ]);

        // 将团队创建人也加入团队成员列表
        $team->users()->attach($user->id);

        // 设置权限为 team owner
        $user->switchTeamTemp($team);
        $user->assignRole('team owner');

        // 如果用户没有默认团队
        if (! $user->current_team_id) {
            $user->switchTeam($team);
        }

        return redirect()->route('teams.show', $team)
            ->with('success', '团队创建成功');
    }

    /**
     * Display the specified team.
     *
     * @return Response
     */
    public function show(Team $team)
    {
        $this->authorize('view', $team);

        setPermissionsTeamId($team->id);
        // 将成员与其在当前团队的角色关联
        $teamUsers = $team->users->map(function ($user) use ($team) {
            $user->roles = $user->roles()->wherePivot('team_id', $team->id)->get();

            return $user;
        });
        $invitations = $team->invitations;
        $owner = User::find($team->owner_id);

        return Inertia::render('Teams/Show', [
            'team' => $team,
            'teamUsers' => $teamUsers,
            'invitations' => $invitations,
            'owner' => $owner,
        ]);
    }

    /**
     * Show the form for editing the specified team.
     *
     * @return Response
     */
    public function edit(Team $team)
    {
        $this->authorize('update', $team);

        return Inertia::render('Teams/Edit', [
            'team' => $team,
        ]);
    }

    /**
     * Update the specified team in storage.
     *
     * @return RedirectResponse
     */
    public function update(Request $request, Team $team)
    {
        $this->authorize('update', $team);

        $this->validate($request, [
            'name' => ['required', 'string', 'max:255'],
            'display_name' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        $team->update([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
        ]);

        return redirect()->route('teams.show', $team)
            ->with('success', '团队信息已更新');
    }

    /**
     * Remove the specified team from storage.
     *
     * @return RedirectResponse
     */
    public function destroy(Team $team)
    {
        $this->authorize('delete', $team);

        $team->delete();

        return redirect()->route('teams.index')
            ->with('success', '团队已删除');
    }

    /**
     * Show form to invite a new member to the team.
     *
     * @return Response
     */
    public function inviteForm(Team $team)
    {
        $this->authorize('update', $team);

        $roles = Role::all();

        return Inertia::render('Teams/Invite', [
            'team' => $team,
            'roles' => $roles,
        ]);
    }

    /**
     * Invite a new member to the team.
     *
     * @return RedirectResponse
     */
    public function invite(Request $request, Team $team)
    {
        $this->authorize('update', $team);

        $this->validate($request, [
            'email' => [
                'required',
                'email',
                Rule::unique('team_invitations')->where(function ($query) use ($team) {
                    return $query->where('team_id', $team->id);
                }),
            ],
            'role' => ['required', 'string'],
        ]);

        // 不能邀请自己
        if ($request->email === $request->user()->email) {
            return redirect()->route('teams.invite', $team)
                ->withErrors(['email' => '不能邀请自己'])
                ->withInput();
        }

        // 不能邀请所有者
        if ($request->email === $team->owner->email) {
            return redirect()->route('teams.invite', $team)
                ->withErrors(['email' => '不能邀请所有者'])
                ->withInput();
        }

        // 检查用户是否已是团队成员
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser && $team->users()->where('users.id', $existingUser->id)->exists()) {
            return redirect()->route('teams.invite', $team)
                ->withErrors(['email' => '该用户已是团队成员'])
                ->withInput();
        }

        $invitation = $team->invitations()->create([
            'email' => $request->email,
            'role' => $request->role,
            'token' => Str::random(40),
        ]);

        // Send invitation email
        // Mail::to($request->email)->send(new TeamInvitation($invitation));

        // 发送实况通知
        $team->notify(new \App\Notifications\TeamEvent(
            'members.invited',
            [
                'email' => $request->email,
                'role' => $request->role,
                'message' => "已向 {$request->email} 发送团队邀请",
            ]
        ));

        return redirect()->route('teams.show', $team)
            ->with('success', '邀请已发送');
    }

    /**
     * Accept a team invitation.
     *
     * @param  string  $token
     * @return RedirectResponse
     */
    public function acceptInvitation($token)
    {
        $invitation = TeamInvitation::where('token', $token)->firstOrFail();
        $team = $invitation->team;
        $user = Auth::user();

        // Check if the user's email matches the invitation
        if ($user->email !== $invitation->email) {
            abort(403);
        }

        // Add user to team
        $team->users()->attach($user);

        // switch
        $user->switchTeam($team);

        // Assign role if specified
        if ($invitation->role) {
            $user->assignRole($invitation->role);
        }

        // Delete the invitation
        $invitation->delete();

        return redirect()->route('teams.show', $team)
            ->with('success', '您已加入团队');
    }

    /**
     * Show the team invitations
     *
     * @return Response
     */
    public function invitations()
    {
        $user = Auth::user();
        $invitations = TeamInvitation::where('email', $user->email)
            ->with('team')
            ->get();

        return Inertia::render('Teams/Invitations', [
            'invitations' => $invitations,
        ]);
    }

    /**
     * Display team roles and permissions management page.
     *
     * @return Response
     */
    public function permissions(Team $team)
    {
        $this->authorize('update', $team);

        $roles = Role::with('permissions')->get();
        $permissions = Permission::all();

        setPermissionsTeamId($team->id);

        $teamUsers = $team->users->map(function ($user) {
            // 获取当前团队的角色
            $user->roles = $user->roles()->get();

            return $user;
        });

        return Inertia::render('Teams/Permissions', [
            'team' => $team,
            'roles' => $roles,
            'permissions' => $permissions,
            'teamUsers' => $teamUsers,
        ]);
    }

    /**
     * Update team member's role.
     *
     * @return RedirectResponse
     */
    public function updateMemberRole(Request $request, Team $team, User $user)
    {
        $this->authorize('update', $team);

        $user->switchTeamTemp($team);

        // 禁止修改团队所有者的角色
        if ($user->id === $team->owner_id) {
            return redirect()->route('teams.permissions', $team)
                ->with('error', '无法修改团队所有者的角色');
        }

        $this->validate($request, [
            'role' => 'required|exists:roles,name',
        ]);

        // Remove all current roles via pivot
        $rolesForTeam = $user->roles()->wherePivot('team_id', $team->id)->get();
        foreach ($rolesForTeam as $role) {
            $user->removeRole($role);
        }

        // Assign new role
        $user->assignRole($request->role);

        return redirect()->route('teams.permissions', $team)
            ->with('success', '角色已更新');
    }

    /**
     * Switch to the given team.
     *
     * @return RedirectResponse
     */
    public function switchTeam(Team $team)
    {
        $user = Auth::user();
        if (! $user->belongsToTeam($team)) {
            abort(403);
        }
        $user->switchTeam($team);

        return back();
    }

    /**
     * Remove a member from the team.
     *
     * @return RedirectResponse
     */
    public function removeMember(Request $request, Team $team, User $user)
    {
        $this->authorize('update', $team);

        // Cannot remove the team owner
        if ($team->owner_id === $user->id) {
            return back()->with('error', '无法移除团队所有者');
        }

        // 不能移除自己
        if ($request->user()->id === $user->id) {
            return back()->with('error', '无法移除自己');
        }

        $team->users()->detach($user);

        return redirect()->route('teams.show', $team)
            ->with('success', '成员已从团队中移除');
    }

    /**
     * Cancel a team invitation.
     *
     * @return RedirectResponse
     */
    public function cancelInvitation(TeamInvitation $invitation)
    {
        $team = $invitation->team;
        $this->authorize('update', $team);

        $invitation->delete();

        return redirect()->route('teams.show', $team)
            ->with('success', '邀请已取消');
    }

    /**
     * Decline a team invitation.
     *
     * @return RedirectResponse
     */
    public function declineInvitation(TeamInvitation $invitation)
    {
        $user = Auth::user();

        // Check if the invitation belongs to the user
        if ($invitation->email !== $user->email) {
            abort(403);
        }

        $invitation->delete();

        return redirect()->route('teams.invitations')
            ->with('success', '已拒绝团队邀请');
    }
}
