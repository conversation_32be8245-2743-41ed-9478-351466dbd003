<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentVersion extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_id',
        'title',
        'description',
        'content',
        'created_by',
        'change_type',
        'change_summary',
        'metadata',
    ];

    protected $casts = [
        'content' => 'array',
        'metadata' => 'array',
    ];

    protected $appends = [
        'short_summary',
    ];

    /**
     * 获取版本所属的文档
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * 获取创建此版本的用户
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 作用域：按文档ID获取版本
     */
    public function scopeForDocument($query, int $documentId)
    {
        return $query->where('document_id', $documentId);
    }

    /**
     * 作用域：按变更类型获取版本
     */
    public function scopeByChangeType($query, string $changeType)
    {
        return $query->where('change_type', $changeType);
    }

    /**
     * 作用域：获取最新版本
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * 获取版本的简短描述
     */
    public function getShortSummaryAttribute(): string
    {
        if ($this->change_summary) {
            return $this->change_summary;
        }

        return match ($this->change_type) {
            'create' => '创建文档',
            'update' => '更新文档',
            'delete' => '删除文档',
            default => '未知操作',
        };
    }

    /**
     * 计算与另一个版本的差异
     */
    public function getDiffWith(DocumentVersion $otherVersion): array
    {
        $changes = [];

        // 标题变更
        if ($this->title !== $otherVersion->title) {
            $changes['title'] = [
                'old' => $otherVersion->title,
                'new' => $this->title,
            ];
        }

        // 描述变更
        if ($this->description !== $otherVersion->description) {
            $changes['description'] = [
                'old' => $otherVersion->description,
                'new' => $this->description,
            ];
        }

        // 内容变更 - 这里可以实现更复杂的内容对比逻辑
        if (json_encode($this->content) !== json_encode($otherVersion->content)) {
            $changes['content'] = [
                'old' => $otherVersion->content,
                'new' => $this->content,
                'has_changes' => true,
            ];
        }

        return $changes;
    }
}
