<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ActivityLog extends Model
{
    use HasFactory;

    /**
     * 批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'team_id',
        'user_id',
        'module',
        'action',
        'description',
        'loggable_type',
        'loggable_id',
        'metadata',
    ];

    /**
     * 应该被转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的团队
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取关联的可记录模型
     */
    public function loggable()
    {
        return $this->morphTo();
    }

    /**
     * 根据团队ID和日期获取活动日志
     *
     * @param  string  $date  格式为 Y-m-d
     * @return Collection
     */
    public static function getByDate(int $teamId, string $date)
    {
        return self::where('team_id', $teamId)
            ->whereDate('created_at', $date)
            ->get();
    }

    /**
     * 获取用户在特定时间范围内的活动日志
     *
     * @param  string  $startDate  格式为 Y-m-d
     * @param  string  $endDate  格式为 Y-m-d
     * @return Collection
     */
    public static function getDateRangeForUser(int $teamId, int $userId, string $startDate, string $endDate)
    {
        return self::where('team_id', $teamId)
            ->where('user_id', $userId)
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->get();
    }

    /**
     * 获取用户按日期分组的活动计数
     *
     * @param  string  $startDate  格式为 Y-m-d
     * @param  string  $endDate  格式为 Y-m-d
     * @return array
     */
    public static function getActivityCountByDate(int $teamId, int $userId, string $startDate, string $endDate)
    {
        return self::where('team_id', $teamId)
            ->where('user_id', $userId)
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->pluck('count', 'date')
            ->toArray();
    }
}
