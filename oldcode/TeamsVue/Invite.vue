<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="邀请" />

        <div class="container p-4">
            <Card class="mx-auto max-w-2xl">
                <CardHeader>
                    <CardTitle>邀请团队成员</CardTitle>
                    <CardDescription>通过邮箱邀请新成员加入团队，并指定其角色。</CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="submit" class="space-y-6">
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <Label for="email">邮箱地址</Label>
                                <Input id="email" v-model="form.email" type="email" required autofocus />
                                <div v-if="form.errors.email" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.email }}</div>
                            </div>

                            <div class="space-y-2">
                                <Label for="role">角色</Label>
                                <select
                                    id="role"
                                    v-model="form.role"
                                    class="focus:border-primary-300 focus:ring-primary-200 focus:ring-opacity-50 mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:ring dark:border-gray-700 dark:bg-gray-900 dark:text-gray-100"
                                >
                                    <option v-for="role in roles" :key="role.id" :value="role.name">
                                        {{ role.name }}
                                    </option>
                                </select>
                                <div v-if="form.errors.role" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.role }}</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end gap-3">
                            <Button type="button" variant="outline" @click="router.visit(route('teams.show', team.id))"> 取消 </Button>
                            <Button type="submit" :disabled="form.processing"> 发送邀请 </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Role } from '@/types/role';
import type { Team } from '@/types/team';
import { Head, router, useForm } from '@inertiajs/vue3';

interface InviteForm {
    email: string;
    role: string;
    [key: string]: string;
}

const props = defineProps<{
    team: Team;
    roles: Role[];
}>();

const form = useForm<InviteForm>({
    email: '',
    role: props.roles.length > 0 ? props.roles[0].name : '',
});

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
    { title: props.team.name, href: route('teams.show', props.team.id) },
    { title: '邀请成员', href: route('teams.invite', props.team.id) },
] as { title: string; href: string }[];

const submit = () => {
    form.post(route('teams.invite', props.team.id));
};
</script>
