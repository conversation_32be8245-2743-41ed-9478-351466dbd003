<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkspaceInvitation extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'email',
        'user_id',
        'token',
        'role',
    ];

    /**
     * 获取邀请所属的工作空间
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 获取被邀请的用户（如果通过用户ID邀请）
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取邀请的标识符（邮箱或用户名）
     */
    public function getInviteeIdentifierAttribute(): string
    {
        if ($this->user_id) {
            return $this->user->name ?? $this->user->email;
        }

        return $this->email;
    }
}
