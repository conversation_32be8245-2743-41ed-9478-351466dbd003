<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="成员管理" />

        <div class="container p-4 space-y-6">
            <!-- 页面标题和操作 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold">成员管理</h1>
                    <p class="text-gray-600 dark:text-gray-400">管理工作空间 "{{ workspace.name }}" 的成员</p>
                </div>
                <Button @click="router.visit(route('workspaces.invite', workspace.id))">
                    <Plus class="mr-2 h-4 w-4" />
                    邀请成员
                </Button>
            </div>

            <!-- 工作空间所有者 -->
            <Card>
                <CardHeader>
                    <CardTitle>工作空间所有者</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="flex items-center space-x-4">
                        <Avatar>
                            <AvatarImage :src="owner.avatar" :alt="owner.name" />
                            <AvatarFallback>{{ owner.name.charAt(0).toUpperCase() }}</AvatarFallback>
                        </Avatar>
                        <div>
                            <p class="font-medium">{{ owner.name }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ owner.email }}</p>
                        </div>
                        <Badge variant="secondary">所有者</Badge>
                    </div>
                </CardContent>
            </Card>

            <!-- 工作空间成员 -->
            <Card>
                <CardHeader>
                    <CardTitle>工作空间成员 ({{ workspaceUsers.length }})</CardTitle>
                </CardHeader>
                <CardContent>
                    <div v-if="workspaceUsers.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        暂无成员
                    </div>
                    <div v-else class="space-y-4">
                        <div v-for="user in workspaceUsers" :key="user.id" class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <Avatar>
                                    <AvatarImage :src="user.avatar" :alt="user.name" />
                                    <AvatarFallback>{{ user.name.charAt(0).toUpperCase() }}</AvatarFallback>
                                </Avatar>
                                <div>
                                    <p class="font-medium">{{ user.name }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500">
                                        加入时间：{{ formatDate(user.joined_at) }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Badge variant="outline">成员</Badge>
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    @click="removeMember(user)"
                                    :disabled="user.id === $page.props.auth.user.id"
                                >
                                    移除
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- 待处理邀请 -->
            <Card>
                <CardHeader>
                    <CardTitle>待处理邀请 ({{ invitations.length }})</CardTitle>
                </CardHeader>
                <CardContent>
                    <div v-if="invitations.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        暂无待处理邀请
                    </div>
                    <div v-else class="space-y-4">
                        <div v-for="invitation in invitations" :key="invitation.id" class="flex items-center justify-between">
                            <div>
                                <p class="font-medium">{{ invitation.email }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400" v-if="invitation.role">
                                    角色：{{ invitation.role }}
                                </p>
                                <p class="text-xs text-gray-400 dark:text-gray-500">
                                    邀请时间：{{ formatDate(invitation.created_at) }}
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Badge variant="secondary">待接受</Badge>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    @click="cancelInvitation(invitation)"
                                >
                                    取消邀请
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Workspace } from '@/types/workspace';
import { Head, router } from '@inertiajs/vue3';
import { Plus } from 'lucide-vue-next';

interface WorkspaceUser {
    id: number;
    name: string;
    email: string;
    avatar: string;
    joined_at: string;
}

interface WorkspaceInvitation {
    id: number;
    email: string;
    role?: string;
    created_at: string;
}

interface Owner {
    id: number;
    name: string;
    email: string;
    avatar: string;
}

const props = defineProps<{
    workspace: Workspace;
    workspaceUsers: WorkspaceUser[];
    invitations: WorkspaceInvitation[];
    owner: Owner;
}>();

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '工作空间', href: route('workspaces.index') },
    { title: props.workspace.name, href: route('workspaces.show', props.workspace.id) },
    { title: '成员管理', href: route('workspaces.members', props.workspace.id) },
] as { title: string; href: string }[];

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const removeMember = (user: WorkspaceUser) => {
    if (confirm(`确定要移除成员 ${user.name} 吗？`)) {
        router.delete(route('workspaces.remove-member', [props.workspace.id, user.id]));
    }
};

const cancelInvitation = (invitation: WorkspaceInvitation) => {
    if (confirm(`确定要取消对 ${invitation.email} 的邀请吗？`)) {
        router.delete(route('workspace-invitations.cancel', invitation.id));
    }
};
</script>
