<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="权限管理" />

        <div class="container space-y-6 p-4">
            <Card>
                <CardHeader>
                    <CardTitle>团队权限管理</CardTitle>
                    <CardDescription>管理团队成员的角色和权限</CardDescription>
                </CardHeader>

                <CardContent>
                    <div
                        v-if="page.props?.flash?.success"
                        class="mb-6 rounded-md border border-green-200 bg-green-50 p-4 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
                    >
                        <div class="flex items-center">
                            <CheckCircle class="mr-2 h-4 w-4" />
                            <div class="font-medium">成功</div>
                        </div>
                        <div class="mt-2 text-sm">{{ page.props.flash.success }}</div>
                    </div>

                    <div
                        v-if="page.props?.flash?.error"
                        class="mb-6 rounded-md border border-red-200 bg-red-50 p-4 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
                    >
                        <div class="flex items-center">
                            <AlertCircle class="mr-2 h-4 w-4" />
                            <div class="font-medium">错误</div>
                        </div>
                        <div class="mt-2 text-sm">{{ page.props.flash.error }}</div>
                    </div>

                    <div class="mb-8">
                        <h3 class="mb-4 text-lg font-medium">角色与权限</h3>

                        <div class="mb-6 rounded-md border border-gray-200 bg-gray-50 p-4 dark:border-gray-800 dark:bg-gray-900/50">
                            <h4 class="text-md mb-3 font-medium">可用角色</h4>
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                <div
                                    v-for="role in roles"
                                    :key="role.id"
                                    class="rounded-md border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
                                >
                                    <div class="flex items-center font-medium">
                                        <ShieldCheck class="mr-2 h-4 w-4 text-blue-500 dark:text-blue-400" />
                                        {{ role.name }}
                                    </div>
                                    <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                        <span class="font-medium">权限：</span>
                                        <span>{{ role.permissions.map((p) => p.name).join(', ') || '无权限' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="mb-4 text-lg font-medium">团队成员角色</h3>

                        <div class="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>用户</TableHead>
                                        <TableHead>当前角色</TableHead>
                                        <TableHead class="text-right">操作</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <TableRow v-for="user in teamUsers" :key="user.id">
                                        <TableCell>
                                            <div>
                                                <div class="font-medium">{{ user.name }}</div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <span
                                                v-if="user.id === props.team.owner_id"
                                                class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                                            >
                                                <Crown class="mr-1 h-3 w-3" />
                                                所有者
                                            </span>
                                            <span
                                                v-else-if="user.roles && user.roles.length > 0"
                                                class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300"
                                            >
                                                <Shield class="mr-1 h-3 w-3" />
                                                {{ user.roles[0].name }}
                                            </span>
                                            <span
                                                v-else
                                                class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                                            >
                                                <User class="mr-1 h-3 w-3" />
                                                无角色
                                            </span>
                                        </TableCell>
                                        <TableCell class="text-right">
                                            <form v-if="user.id !== props.team.owner_id" @submit.prevent="updateUserRole(user)">
                                                <div class="flex items-center justify-end space-x-2">
                                                    <select
                                                        v-model="userRoles[user.id]"
                                                        class="border-input bg-background focus-visible:ring-ring h-9 w-[180px] rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900"
                                                    >
                                                        <option v-for="role in roles" :key="role.id" :value="role.name">
                                                            {{ role.name }}
                                                        </option>
                                                    </select>
                                                    <Button type="submit" size="sm">
                                                        <Save class="mr-1 h-4 w-4" />
                                                        更新
                                                    </Button>
                                                </div>
                                            </form>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div class="mt-6">
                        <Button variant="outline" @click="router.visit(route('teams.show', props.team.id))">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            返回团队详情
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, usePage } from '@inertiajs/vue3';
import { onMounted, ref } from 'vue';

import { AlertCircle, ArrowLeft, CheckCircle, Crown, Save, Shield, ShieldCheck, User } from 'lucide-vue-next';

const page = usePage();

interface Permission {
    id: number;
    name: string;
    guard_name: string;
}

interface Role {
    id: number;
    name: string;
    guard_name: string;
    permissions: Permission[];
}

interface Team {
    id: number;
    name: string;
    display_name: string | null;
    description: string | null;
    owner_id: number;
}

interface User {
    id: number;
    name: string;
    email: string;
    roles?: Role[];
}

const props = defineProps<{
    team: Team;
    roles: Role[];
    permissions: Permission[];
    teamUsers: User[];
}>();

const userRoles = ref<Record<number, string>>({});

onMounted(() => {
    // 初始化用户角色选择
    props.teamUsers.forEach((user) => {
        if (user.id !== props.team.owner_id) {
            userRoles.value[user.id] = user.roles && user.roles.length > 0 ? user.roles[0].name : props.roles.length > 0 ? props.roles[0].name : '';
        }
    });
});

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
    { title: props.team.display_name || props.team.name, href: route('teams.show', props.team.id) },
    { title: '权限管理', href: route('teams.permissions', props.team.id) },
] as { title: string; href: string }[];

const updateUserRole = (user: User) => {
    const selectedRole = userRoles.value[user.id];

    router.put(route('teams.update-member-role', [props.team.id, user.id]), {
        role: selectedRole,
    });
};
</script>
