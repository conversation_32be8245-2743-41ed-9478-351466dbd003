<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class File extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'hash',
        'path',
        'size',
        'mime_type',
        'reference_count',
        'last_used_at',
        'thumbnails',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_used_at' => 'datetime',
        'thumbnails' => 'array',
    ];

    /**
     * Get the media items that reference this file.
     */
    public function mediaItems(): HasMany
    {
        return $this->hasMany(Media::class);
    }

    /**
     * 获取文件二进制数据
     */
    public function getBinaryData(): ?string
    {
        return Storage::disk('s3')->get($this->path);
    }
}
