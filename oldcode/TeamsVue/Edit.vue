<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="编辑团队" />

        <div class="container p-4">
            <Card class="mx-auto max-w-2xl">
                <CardHeader>
                    <CardTitle>编辑团队</CardTitle>
                    <CardDescription>更新团队信息</CardDescription>
                </CardHeader>

                <CardContent>
                    <form @submit.prevent="submit" class="space-y-6">
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <Label for="name">团队名称</Label>
                                <Input id="name" v-model="form.name" type="text" required autofocus />
                                <div v-if="form.errors.name" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.name }}</div>
                            </div>

                            <div class="space-y-2">
                                <Label for="display_name">显示名称（可选）</Label>
                                <Input id="display_name" v-model="form.display_name" type="text" />
                                <div v-if="form.errors.display_name" class="text-sm text-red-500 dark:text-red-400">
                                    {{ form.errors.display_name }}
                                </div>
                            </div>

                            <div class="space-y-2">
                                <Label for="description">团队描述（可选）</Label>
                                <Textarea id="description" v-model="form.description" rows="3" />
                                <div v-if="form.errors.description" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.description }}</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end gap-3">
                            <Button type="button" variant="outline" @click="router.visit(route('teams.show', team.id))">
                                <ArrowLeft class="mr-2 h-4 w-4" />
                                取消
                            </Button>
                            <Button type="submit" :disabled="form.processing">
                                <Save class="mr-2 h-4 w-4" />
                                保存更改
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Team } from '@/types/team';
import { Head, router, useForm } from '@inertiajs/vue3';
import { ArrowLeft, Save } from 'lucide-vue-next';

interface TeamForm {
    name: string;
    display_name: string;
    description: string;
    [key: string]: string;
}

const props = defineProps<{
    team: Team;
}>();

const form = useForm<TeamForm>({
    name: props.team.name,
    display_name: props.team.display_name || '',
    description: props.team.description || '',
});

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
    { title: props.team.display_name || props.team.name, href: route('teams.show', props.team.id) },
    { title: '编辑团队', href: route('teams.edit', props.team.id) },
] as { title: string; href: string }[];

const submit = () => {
    form.put(route('teams.update', props.team.id));
};
</script>
