<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Workspace;
use App\Models\WorkspaceInvitation;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class WorkspaceInvitationController extends Controller
{
    /**
     * 显示邀请成员表单
     */
    public function inviteForm(Workspace $workspace): Response
    {
        $this->authorize('update', $workspace);

        return Inertia::render('Workspaces/Invite', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 邀请新成员加入工作空间
     */
    public function invite(Request $request, Workspace $workspace): RedirectResponse
    {
        $this->authorize('update', $workspace);

        $this->validate($request, [
            'email' => [
                'required',
                'email',
                Rule::unique('workspace_invitations')->where(function ($query) use ($workspace) {
                    return $query->where('workspace_id', $workspace->id);
                }),
            ],
            'role' => ['nullable', 'string'],
        ]);

        // 不能邀请自己
        if ($request->email === $request->user()->email) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['email' => '不能邀请自己'])
                ->withInput();
        }

        // 不能邀请所有者
        if ($request->email === $workspace->user->email) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['email' => '不能邀请工作空间所有者'])
                ->withInput();
        }

        // 检查用户是否已是工作空间成员
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser && $workspace->hasUser($existingUser)) {
            return redirect()->route('workspaces.invite', $workspace)
                ->withErrors(['email' => '该用户已是工作空间成员'])
                ->withInput();
        }

        $invitation = $workspace->invitations()->create([
            'email' => $request->email,
            'role' => $request->role,
            'token' => Str::random(40),
        ]);

        // TODO: 发送邀请邮件
        // Mail::to($request->email)->send(new WorkspaceInvitation($invitation));

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '邀请已发送');
    }

    /**
     * 接受工作空间邀请
     */
    public function acceptInvitation(string $token): RedirectResponse
    {
        $invitation = WorkspaceInvitation::where('token', $token)->firstOrFail();
        $workspace = $invitation->workspace;
        $user = Auth::user();

        // 检查邀请的邮箱是否匹配当前用户
        if ($user->email !== $invitation->email) {
            abort(403, '邀请邮箱与当前用户不匹配');
        }

        // 将用户添加到工作空间
        $workspace->users()->attach($user);

        // 如果用户没有当前工作空间，设置为当前工作空间
        if (!$user->current_workspace_id) {
            $user->setCurrentWorkspace($workspace);
        }

        // TODO: 分配角色（如果指定了角色）
        // if ($invitation->role) {
        //     $user->assignRole($invitation->role);
        // }

        // 删除邀请
        $invitation->delete();

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '您已成功加入工作空间');
    }

    /**
     * 显示用户的邀请列表
     */
    public function invitations(): Response
    {
        $user = Auth::user();
        $invitations = WorkspaceInvitation::where('email', $user->email)
            ->with('workspace')
            ->get();

        return Inertia::render('Workspaces/Invitations', [
            'invitations' => $invitations,
        ]);
    }

    /**
     * 拒绝工作空间邀请
     */
    public function declineInvitation(WorkspaceInvitation $invitation): RedirectResponse
    {
        $user = Auth::user();

        // 检查邀请是否属于当前用户
        if ($invitation->email !== $user->email) {
            abort(403, '无权操作此邀请');
        }

        $invitation->delete();

        return redirect()->route('workspace-invitations.index')
            ->with('success', '已拒绝工作空间邀请');
    }

    /**
     * 取消工作空间邀请（工作空间管理员操作）
     */
    public function cancelInvitation(WorkspaceInvitation $invitation): RedirectResponse
    {
        $workspace = $invitation->workspace;
        $this->authorize('update', $workspace);

        $invitation->delete();

        return redirect()->route('workspaces.show', $workspace)
            ->with('success', '邀请已取消');
    }
}
