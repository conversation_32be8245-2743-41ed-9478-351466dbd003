<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class DocumentShare extends Model
{
    use HasFactory;

    protected $fillable = [
        'share_id',
        'document_id',
        'team_id',
        'created_by',
        'title',
        'description',
        'is_active',
        'expires_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * 模型启动方法
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($documentShare) {
            // 生成唯一的分享ID
            if (empty($documentShare->share_id)) {
                $documentShare->share_id = static::generateUniqueShareId();
            }
        });
    }

    /**
     * 生成唯一的分享ID
     */
    protected static function generateUniqueShareId(): string
    {
        do {
            $shareId = Str::random(16);
        } while (static::where('share_id', $shareId)->exists());

        return $shareId;
    }

    /**
     * 获取关联的文档
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * 获取所属团队
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取创建分享的用户
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 检查分享是否有效
     */
    public function isValid(): bool
    {
        if (! $this->is_active) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * 获取有效的分享
     */
    public function scopeValid($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * 根据分享ID查找有效的分享
     */
    public static function findValidByShareId(string $shareId): ?self
    {
        return static::where('share_id', $shareId)
            ->valid()
            ->first();
    }

    /**
     * 获取公开访问URL
     */
    public function getPublicUrl(): string
    {
        return route('public.document', $this->share_id);
    }

    /**
     * 获取显示标题
     */
    public function getDisplayTitle(): string
    {
        return $this->title ?: $this->document->title ?: '未命名文档';
    }
}
