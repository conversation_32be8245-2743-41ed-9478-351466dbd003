<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="邀请成员" />

        <div class="container p-4">
            <Card class="mx-auto max-w-2xl">
                <CardHeader>
                    <CardTitle>邀请工作空间成员</CardTitle>
                    <CardDescription>通过邮箱邀请新成员加入工作空间。</CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="submit" class="space-y-6">
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <Label for="email">邮箱地址</Label>
                                <Input id="email" v-model="form.email" type="email" required autofocus />
                                <div v-if="form.errors.email" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.email }}</div>
                            </div>

                            <div class="space-y-2">
                                <Label for="role">角色（可选）</Label>
                                <Input id="role" v-model="form.role" type="text" placeholder="例如：开发者、管理员等" />
                                <div v-if="form.errors.role" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.role }}</div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">角色仅用于标识，暂不影响权限</p>
                            </div>
                        </div>

                        <div class="flex items-center justify-end gap-3">
                            <Button type="button" variant="outline" @click="router.visit(route('workspaces.show', workspace.id))"> 取消 </Button>
                            <Button type="submit" :disabled="form.processing"> 发送邀请 </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Workspace } from '@/types/workspace';
import { Head, router, useForm } from '@inertiajs/vue3';

interface InviteForm {
    email: string;
    role: string;
    [key: string]: string;
}

const props = defineProps<{
    workspace: Workspace;
}>();

const form = useForm<InviteForm>({
    email: '',
    role: '',
});

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '工作空间', href: route('workspaces.index') },
    { title: props.workspace.name, href: route('workspaces.show', props.workspace.id) },
    { title: '邀请成员', href: route('workspaces.invite', props.workspace.id) },
] as { title: string; href: string }[];

const submit = () => {
    form.post(route('workspaces.invite.store', props.workspace.id));
};
</script>
