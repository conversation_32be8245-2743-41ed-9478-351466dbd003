<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workspace_invitations', function (Blueprint $table) {
            $table->id();

            // 工作空间 ID
            $table->foreignId('workspace_id')->constrained('workspaces')->onDelete('cascade');

            // 被邀请的邮箱（可选，用于邮箱邀请）
            $table->string('email')->nullable();

            // 被邀请的用户ID（可选，用于用户ID邀请）
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');

            // 邀请令牌
            $table->string('token')->unique();

            // 角色（预留给权限系统）
            $table->string('role')->nullable();

            $table->timestamps();

            // 确保同一个工作空间不能重复邀请同一个邮箱或用户
            $table->unique(['workspace_id', 'email']);
            $table->unique(['workspace_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workspace_invitations');
    }
};
