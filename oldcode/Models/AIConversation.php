<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class AIConversation extends Model
{
    use HasFactory;

    protected $table = 'ai_conversations';

    protected $fillable = [
        'conversation_id',
        'team_id',
        'user_id',
        'title',
    ];

    /**
     * 创建新对话前生成UUID
     */
    protected static function booted()
    {
        static::creating(function ($conversation) {
            if (empty($conversation->conversation_id)) {
                $conversation->conversation_id = (string) Str::uuid();
            }
        });
    }

    /**
     * 获取与对话关联的消息
     */
    public function messages(): HasMany
    {
        return $this->hasMany(AIMessage::class, 'conversation_id', 'conversation_id');
    }

    /**
     * 获取对话所属的团队
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取对话所属的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
