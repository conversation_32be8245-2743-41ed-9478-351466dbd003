<template>
    <TeamsSettingsLayout :team="team">
        <div class="space-y-6">
            <div>
                <h3 class="text-lg font-medium">团队基本信息</h3>
                <p class="text-muted-foreground text-sm">更新团队的基本信息和描述</p>
            </div>

            <Separator />

            <form @submit.prevent="submit" class="space-y-6">
                <div class="space-y-4">
                    <div class="space-y-2">
                        <Label for="name">团队名称</Label>
                        <Input id="name" v-model="form.name" type="text" required autofocus />
                        <InputError :message="form.errors.name" />
                    </div>

                    <div class="space-y-2">
                        <Label for="display_name">显示名称（可选）</Label>
                        <Input id="display_name" v-model="form.display_name" type="text" />
                        <InputError :message="form.errors.display_name" />
                    </div>

                    <div class="space-y-2">
                        <Label for="description">团队描述（可选）</Label>
                        <Textarea id="description" v-model="form.description" rows="3" />
                        <InputError :message="form.errors.description" />
                    </div>
                </div>

                <div class="flex items-center justify-end gap-3">
                    <Button type="button" variant="outline" @click="router.visit(route('teams.show', team.id))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回团队
                    </Button>
                    <Button type="submit" :disabled="form.processing">
                        <Save class="mr-2 h-4 w-4" />
                        保存更改
                    </Button>
                </div>
            </form>
        </div>
    </TeamsSettingsLayout>
</template>

<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import TeamsSettingsLayout from '@/layouts/teams/SettingsLayout.vue';
import { router, useForm } from '@inertiajs/vue3';
import { ArrowLeft, Save } from 'lucide-vue-next';

interface Team {
    id: number;
    name: string;
    display_name?: string;
    description?: string;
}

interface TeamForm {
    name: string;
    display_name: string;
    description: string;
    [key: string]: string;
}

const props = defineProps({
    team: {
        type: Object as () => Team,
        required: true,
    },
});

const form = useForm<TeamForm>({
    name: props.team.name,
    display_name: props.team.display_name || '',
    description: props.team.description || '',
});

const submit = () => {
    form.put(route('teams.settings.general.update', props.team.id));
};
</script>
