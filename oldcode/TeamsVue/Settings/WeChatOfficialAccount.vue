<template>
    <settings-layout :team="team">
        <Head title="微信订阅号设置" />
        <div class="space-y-6">
            <div>
                <h3 class="text-lg font-medium">微信公众号设置</h3>
                <p class="text-muted-foreground text-sm">配置您的微信公众号信息，用于接收和发送微信公众平台消息</p>
            </div>
            <Separator />

            <form @submit.prevent="saveSettings">
                <div class="space-y-6">
                    <!-- AppID -->
                    <div>
                        <Label for="app_id" class="mb-2 block">AppID</Label>
                        <Input id="app_id" v-model="form.app_id" type="text" class="w-full" placeholder="请输入 AppID" />
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">必填，微信公众平台应用ID</p>
                        <InputError :message="form.errors.app_id" class="mt-2" />
                    </div>
                    <div>
                        <Label for="secret" class="mb-2 block">开发者密码</Label>
                        <Input id="secret" v-model="form.secret" type="password" class="w-full" placeholder="请输入 Secret" />
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">必填，微信公众平台开发者密码</p>
                        <InputError :message="form.errors.secret" class="mt-2" />
                    </div>

                    <div>
                        <Label for="token" class="mb-2 block">令牌(Token)</Label>
                        <Input id="token" v-model="form.token" type="text" class="w-full" placeholder="请输入令牌" />
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">必填，用于消息加解密</p>
                        <InputError :message="form.errors.token" class="mt-2" />
                    </div>

                    <div>
                        <Label for="aes_key" class="mb-2 block">消息加解密密钥(EncodingAESKey)</Label>
                        <Input id="aes_key" v-model="form.aes_key" type="text" class="w-full" placeholder="请输入消息加解密密钥" />
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">选填，加密模式下需要填写</p>
                        <InputError :message="form.errors.aes_key" class="mt-2" />
                    </div>

                    <div class="mb-6">
                        <div class="flex items-center space-x-2">
                            <Checkbox id="is_active" v-model="form.is_active" />
                            <Label for="is_active">启用微信公众号集成</Label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">启用后将接收和处理微信公众平台消息</p>
                    </div>

                    <p>服务器配置的回调地址为：{{ route('api.wechat-official-account.serve', props.team.id) }}</p>

                    <div class="flex justify-end">
                        <Button type="submit" :disabled="form.processing">保存设置</Button>
                    </div>
                </div>
            </form>
        </div>
    </settings-layout>
</template>

<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import SettingsLayout from '@/layouts/teams/SettingsLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { toast } from 'vue-sonner';

interface Team {
    id: number;
    name: string;
}

interface WeChatSettings {
    id: string;
    app_id: string;
    secret: string;
    token: string;
    aes_key: string;
    is_active: boolean;
}

const props = defineProps<{
    team: Team;
    wechatSettings: WeChatSettings;
}>();

const form = useForm({
    id: props.wechatSettings.id || '',
    app_id: props.wechatSettings.app_id || '',
    secret: props.wechatSettings.secret || '',
    token: props.wechatSettings.token || '',
    aes_key: props.wechatSettings.aes_key || '',
    is_active: props.wechatSettings.is_active,
});

const saveSettings = () => {
    form.post(route('teams.settings.wechat-official-account.update', props.team.id), {
        onSuccess: () => {
            toast.success('微信公众号设置已保存');
        },
    });
};
</script>
