<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Integration extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'name',
        'url',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 获取所属团队
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取团队的活跃集成
     */
    public static function getActiveIntegrationsForTeam(int $teamId): Collection
    {
        return static::where('team_id', $teamId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }
}
