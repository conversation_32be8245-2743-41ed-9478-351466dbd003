<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="团队" />

        <div class="container space-y-6 p-4">
            <Card class="overflow-hidden">
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-xl">我的团队</CardTitle>
                    <Button @click="router.visit(route('teams.create'))">
                        <PlusCircle class="mr-2 h-4 w-4" />
                        创建团队
                    </Button>
                </CardHeader>
                <CardContent>
                    <div v-if="teams.length > 0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>团队名称</TableHead>
                                    <TableHead>描述</TableHead>
                                    <TableHead class="text-right">操作</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="team in teams" :key="team.id">
                                    <TableCell>
                                        <div class="flex items-center">
                                            <span>{{ team.display_name || team.name }}</span>
                                            <span
                                                v-if="currentTeam && currentTeam.id === team.id"
                                                class="ml-2 inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300"
                                            >
                                                当前
                                            </span>
                                        </div>
                                    </TableCell>
                                    <TableCell>{{ team.description || '无描述' }}</TableCell>

                                    <TableCell class="text-right">
                                        <div class="flex justify-end space-x-2">
                                            <Button variant="outline" size="sm" @click="router.visit(route('teams.show', team.id))">
                                                <Eye class="mr-1 h-4 w-4" />
                                                查看
                                            </Button>
                                            <Button
                                                v-if="currentTeam ? currentTeam.id != team.id : true"
                                                variant="secondary"
                                                size="sm"
                                                @click="switchTeam(team.id)"
                                            >
                                                <RefreshCw class="mr-1 h-4 w-4" />
                                                使用
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                    <div
                        v-else
                        class="mt-4 rounded-md border border-blue-200 bg-blue-50 p-4 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
                    >
                        <div class="flex items-center">
                            <Info class="mr-2 h-4 w-4" />
                            <div class="font-medium">没有团队</div>
                        </div>
                        <div class="mt-2 text-sm">您还没有加入任何团队。创建您的第一个团队开始使用。</div>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>团队邀请</CardTitle>
                    <CardDescription>查看并管理您收到的团队邀请</CardDescription>
                </CardHeader>
                <CardContent>
                    <Button variant="outline" @click="router.visit(route('teams.invitations'))">
                        <Mail class="mr-2 h-4 w-4" />
                        查看待处理的邀请 ({{ invitationCount }})
                    </Button>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>联系我们</CardTitle>
                    <CardDescription>我们期待您的反馈和建议</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="text-muted-foreground text-sm">
                        <p class="mb-2">
                            我们正在精心打造这款产品，用心倾听每一位用户的声音。如果您有任何功能建议或发现了问题，我们将非常感谢您的反馈。
                        </p>
                        <p>联系我们：微信: the_web_artisan / QQ: 2596048743。请注明来意。</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
// import type { User } from '@/types';
import type { Team } from '@/types/team';
import { Head, router } from '@inertiajs/vue3';
import { Eye, Info, Mail, PlusCircle, RefreshCw } from 'lucide-vue-next';
// import { computed } from 'vue';

defineProps<{
    teams: Team[];
    currentTeam: Team | null;
    invitationCount: number;
}>();

// const user = computed<User>(() => page.props.auth.user);

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
] as { title: string; href: string }[];

const switchTeam = (teamId: number) => {
    router.post(
        route('teams.switch', teamId),
        {},
        {
            onSuccess: () => {
                window.location.reload();
            },
        },
    );
};
</script>
