<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="团队" />

        <div class="container space-y-6 p-4">
            <Card class="mb-6 overflow-hidden">
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-xl">{{ team.display_name || team.name }}</CardTitle>
                    <Button v-if="!isCurrent" variant="secondary" size="sm" @click="switchTeam">
                        <RefreshCw class="mr-2 h-4 w-4" />
                        使用
                    </Button>
                    <div class="flex flex-wrap gap-2">
                        <Button v-if="can('team:update')" @click="router.visit(route('teams.settings.general', team.id))" variant="outline" size="sm">
                            <Settings class="mr-2 h-4 w-4" />
                            团队设置
                        </Button>
                        <Button v-if="can('team:update')" @click="router.visit(route('teams.invite', team.id))" variant="outline" size="sm">
                            <UserPlus class="mr-2 h-4 w-4" />
                            邀请成员
                        </Button>
                        <Button v-if="can('team:update')" @click="router.visit(route('teams.permissions', team.id))" variant="outline" size="sm">
                            <Lock class="mr-2 h-4 w-4" />
                            权限管理
                        </Button>
                        <Button v-if="can('team:delete')" @click="confirmTeamDeletion" variant="destructive" size="sm">
                            <Trash2 class="mr-2 h-4 w-4" />
                            删除团队
                        </Button>
                    </div>
                </CardHeader>

                <CardContent>
                    <div
                        v-if="page.props?.flash?.success"
                        class="mb-6 rounded-md border border-green-200 bg-green-50 p-4 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
                    >
                        <div class="flex items-center">
                            <CheckCircle class="mr-2 h-4 w-4" />
                            <div class="font-medium">成功</div>
                        </div>
                        <div class="mt-2 text-sm">{{ page.props.flash.success }}</div>
                    </div>

                    <div
                        v-if="page.props?.flash?.error"
                        class="mb-6 rounded-md border border-red-200 bg-red-50 p-4 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
                    >
                        <div class="flex items-center">
                            <AlertCircle class="mr-2 h-4 w-4" />
                            <div class="font-medium">错误</div>
                        </div>
                        <div class="mt-2 text-sm">{{ page.props.flash.error }}</div>
                    </div>

                    <div class="mb-8">
                        <h3 class="mb-4 text-lg font-medium">团队信息</h3>
                        <div class="rounded-md border border-gray-200 bg-gray-50 p-4 dark:border-gray-800 dark:bg-gray-900/50">
                            <dl class="grid grid-cols-1 gap-x-4 gap-y-3 md:grid-cols-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">团队名称</dt>
                                    <dd class="mt-1 text-sm">{{ team.name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">显示名称</dt>
                                    <dd class="mt-1 text-sm">{{ team.display_name || '无' }}</dd>
                                </div>
                                <div class="col-span-2">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">描述</dt>
                                    <dd class="mt-1 text-sm">{{ team.description || '无描述' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间</dt>
                                    <dd class="mt-1 text-sm">{{ formatDate(team.created_at) }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">团队所有者</dt>
                                    <dd class="mt-1 text-sm">{{ owner.name }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h3 class="mb-4 text-lg font-medium">团队成员</h3>
                        <div class="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>用户</TableHead>
                                        <TableHead>角色</TableHead>
                                        <TableHead>加入时间</TableHead>
                                        <TableHead class="text-right">操作</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <TableRow v-for="teamUser in teamUsers" :key="teamUser.id">
                                        <TableCell>
                                            <div>
                                                <div class="font-medium">{{ teamUser.name }}</div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ teamUser.email }}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <span
                                                v-if="teamUser.id === team.owner_id"
                                                class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                                            >
                                                创建者
                                            </span>
                                            <span
                                                v-else-if="teamUser.roles && teamUser.roles.length > 0"
                                                class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300"
                                            >
                                                {{ teamUser.roles[0].name }}
                                            </span>
                                            <span
                                                v-else
                                                class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                                            >
                                                成员
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            {{ formatDate(teamUser.pivot?.created_at) }}
                                        </TableCell>
                                        <TableCell class="text-right">
                                            <Button
                                                v-if="can('team:update') && teamUser.id !== team.owner_id"
                                                variant="outline"
                                                size="sm"
                                                @click="confirmMemberRemoval(teamUser)"
                                            >
                                                <UserMinus class="mr-1 h-4 w-4" />
                                                移除
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div v-if="invitations.length > 0" class="mb-8">
                        <h3 class="mb-4 text-lg font-medium">待处理邀请</h3>
                        <div class="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>邮箱</TableHead>
                                        <TableHead>角色</TableHead>
                                        <TableHead>邀请时间</TableHead>
                                        <TableHead class="text-right">操作</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <TableRow v-for="invitation in invitations" :key="invitation.id">
                                        <TableCell>
                                            <div>{{ invitation.email }}</div>
                                        </TableCell>
                                        <TableCell>
                                            <span
                                                class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                                            >
                                                {{ invitation.role }}
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            {{ formatDate(invitation.created_at) }}
                                        </TableCell>
                                        <TableCell class="text-right">
                                            <Button v-if="can('team:update')" variant="outline" size="sm" @click="cancelInvitation(invitation)">
                                                <X class="mr-1 h-4 w-4" />
                                                取消邀请
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <!-- 删除团队确认对话框 -->
        <Dialog :open="showDeleteTeamDialog" @update:open="showDeleteTeamDialog = false">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>确认删除团队</DialogTitle>
                    <DialogDescription> 您确定要删除团队 "{{ team.display_name || team.name }}" 吗？此操作无法撤销。 </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" @click="showDeleteTeamDialog = false">取消</Button>
                    <Button @click="deleteTeam" variant="destructive">删除</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        <!-- 移除成员确认对话框 -->
        <Dialog :open="showRemoveMemberDialog" @update:open="showRemoveMemberDialog = false">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>确认移除成员</DialogTitle>
                    <DialogDescription> 您确定要移除成员 {{ memberToRemove?.name }} 吗？ </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" @click="showRemoveMemberDialog = false">取消</Button>
                    <Button @click="removeMember" variant="destructive">移除</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        <!-- 取消邀请确认对话框 -->
        <Dialog :open="showCancelInvitationDialog" @update:open="showCancelInvitationDialog = false">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>确认取消邀请</DialogTitle>
                    <DialogDescription> 您确定要取消发送给 {{ invitationToCancel?.email }} 的邀请吗？ </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" @click="showCancelInvitationDialog = false">取消</Button>
                    <Button @click="confirmCancelInvitation" variant="destructive">确认取消</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, usePage } from '@inertiajs/vue3';
import { AlertCircle, CheckCircle, Lock, RefreshCw, Settings, Trash2, UserMinus, UserPlus, X } from 'lucide-vue-next';
import { computed, ref } from 'vue';

import type { Team } from '@/types/team';
import type { User } from '../../types';

interface TeamInvitation {
    id: number;
    team_id: number;
    email: string;
    role: string;
    token: string;
    created_at: string;
    updated_at: string;
}

import type { TeamUser } from '@/types/team';

interface Props {
    team: Team;
    owner: User;
    teamUsers: TeamUser[];
    invitations: TeamInvitation[];
}

const props = defineProps<Props>();

const page = usePage();
const user = computed<User>(() => page.props.auth.user as User);
const isCurrent = computed(() => user.value.current_team_id === props.team.id);

const switchTeam = () => {
    router.post(
        route('teams.switch', props.team.id),
        {},
        {
            onSuccess: () => window.location.reload(),
        },
    );
};

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
    { title: props.team.display_name || props.team.name, href: route('teams.show', props.team.id) },
] as { title: string; href: string }[];

const formatDate = (dateString?: string) => {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
};

// 删除团队对话框控制
const showDeleteTeamDialog = ref(false);

const confirmTeamDeletion = () => {
    showDeleteTeamDialog.value = true;
};

const deleteTeam = () => {
    router.delete(route('teams.destroy', props.team.id));
    showDeleteTeamDialog.value = false;
};

// 移除成员对话框控制
const showRemoveMemberDialog = ref(false);
const memberToRemove = ref<TeamUser | null>(null);

const confirmMemberRemoval = (member: TeamUser) => {
    memberToRemove.value = member;
    showRemoveMemberDialog.value = true;
};

const removeMember = () => {
    if (memberToRemove.value) {
        router.delete(route('teams.remove-member', [props.team.id, memberToRemove.value.id]));
        showRemoveMemberDialog.value = false;
    }
};

// 取消邀请对话框控制
const showCancelInvitationDialog = ref(false);
const invitationToCancel = ref<TeamInvitation | null>(null);

const cancelInvitation = (invitation: TeamInvitation) => {
    invitationToCancel.value = invitation;
    showCancelInvitationDialog.value = true;
};

const confirmCancelInvitation = () => {
    if (invitationToCancel.value) {
        router.delete(route('teams.invitations.cancel', invitationToCancel.value.id));
        showCancelInvitationDialog.value = false;
    }
};
</script>
