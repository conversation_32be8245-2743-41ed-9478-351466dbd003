<?php

namespace App\Models;

use App\Traits\HasSettings;
// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Database\Factories\UserFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<UserFactory> */
    use HasApiTokens, HasFactory, HasRoles, HasSettings, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'current_team_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get all of the teams that the user owns.
     */
    public function ownedTeams()
    {
        return $this->hasMany(Team::class, 'owner_id');
    }

    /**
     * Get all of the teams that the user belongs to.
     */
    public function teams()
    {
        return $this->belongsToMany(Team::class)->withTimestamps();
    }

    /**
     * Get the current team of the user.
     */
    public function currentTeam(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'current_team_id');
    }

    // 不再需要用户订阅日历的关系方法，因为所有日历都属于团队

    /**
     * Switch the user's context to the given team.
     */
    public function switchTeam(Team $team)
    {
        if (! $this->belongsToTeam($team)) {
            abort(403);
        }

        $this->forceFill([
            'current_team_id' => $team->id,
        ])->save();

        setPermissionsTeamId($team->id);

        return true;
    }

    /**
     * Switch the user's context to the given team.
     */
    public function switchTeamTemp(Team $team)
    {
        if (! $this->belongsToTeam($team)) {
            abort(403);
        }

        setPermissionsTeamId($team->id);

        return true;
    }

    /**
     * Determine if the user belongs to the given team.
     */
    public function belongsToTeam(Team $team)
    {
        return $this->teams->contains(function ($t) use ($team) {
            return $t->id === $team->id;
        }) || $this->ownedTeams->contains(function ($t) use ($team) {
            return $t->id === $team->id;
        });
    }

    /**
     * Get all the teams that the user has a role in.
     */
    public function teamsWithRole()
    {
        return $this->teams->merge($this->ownedTeams);
    }

    /**
     * 判断用户是否在指定 ID 的团队中
     *
     * @param  int|Team|null  $team  团队模型或者 ID
     * @return bool 用户是否在该团队中
     */
    public function inTeamId(int|Team|null $team): bool
    {
        // 如果团队 ID 为 null，直接返回 false
        if ($team === null) {
            return false;
        }

        $teamId = is_int($team) ? $team : $team->id;

        // 先检查当前团队，如果当前团队 ID 匹配，可以快速返回
        if ($this->currentTeam && $this->currentTeam->id === $teamId) {
            return true;
        }

        // 再检查拥有的团队，这通常是小集合，性能影响小
        return $this->ownedTeams->contains('id', $teamId) ||
            $this->teams->contains('id', $teamId);
    }

    /**
     * Determine if the user has the given permission on the given team.
     */
    public function hasTeamPermission($team, $permission)
    {
        if ($this->id === $team->owner_id) {
            return true;
        }

        if (is_string($team)) {
            $team = Team::where('id', $team)->first();
        }

        return array_any($this->roles()->wherePivot('team_id', $team->id)->get(), fn ($role) => $role->hasPermissionTo($permission));

    }

    /**
     * 获取用于 Bark 通知的路由
     *
     * @return string|null
     */
    public function routeNotificationForBark()
    {
        // 使用 HasSettings trait 获取 Bark 设置
        $barkServer = $this->getSetting('notifications:bark_server');
        $barkToken = $this->getSetting('notifications:bark_token');

        if (empty($barkServer) || empty($barkToken)) {
            return null;
        }

        return $barkToken;
    }

    public function getTeam(): ?Team
    {
        $teamId = getPermissionsTeamId() ?? $this->current_team_id;
        if (! $teamId) {
            return null;
        }

        return Team::find($teamId);

    }

    public function ownsTeam(Team $team)
    {
        return $this->id === $team->owner_id;
    }
}
