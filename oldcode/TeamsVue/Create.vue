<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="container p-4">
            <Card class="mx-auto max-w-2xl">
                <CardHeader>
                    <CardTitle>创建新团队</CardTitle>
                    <CardDescription>创建一个新的团队并邀请成员加入</CardDescription>
                </CardHeader>

                <CardContent>
                    <form @submit.prevent="submit" class="space-y-6">
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <Label for="name">团队名称</Label>
                                <Input id="name" v-model="form.name" type="text" required autofocus />
                                <div v-if="form.errors.name" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.name }}</div>
                            </div>

                            <div class="space-y-2">
                                <Label for="display_name">显示名称（可选）</Label>
                                <Input id="display_name" v-model="form.display_name" type="text" />
                                <div v-if="form.errors.display_name" class="text-sm text-red-500 dark:text-red-400">
                                    {{ form.errors.display_name }}
                                </div>
                            </div>

                            <div class="space-y-2">
                                <Label for="description">团队描述（可选）</Label>
                                <Textarea id="description" v-model="form.description" rows="3" />
                                <div v-if="form.errors.description" class="text-sm text-red-500 dark:text-red-400">{{ form.errors.description }}</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end gap-3">
                            <Button type="button" variant="outline" @click="router.visit(route('teams.index'))">
                                <ArrowLeft class="mr-2 h-4 w-4" />
                                取消
                            </Button>
                            <Button type="submit" :disabled="form.processing">
                                <PlusCircle class="mr-2 h-4 w-4" />
                                创建团队
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/AppLayout.vue';
import { router, useForm } from '@inertiajs/vue3';
import { ArrowLeft, PlusCircle } from 'lucide-vue-next';

type TeamForm = {
    name: string;
    display_name: string;
    description: string;
} & Record<string, any>;

const form = useForm<TeamForm>({
    name: '',
    display_name: '',
    description: '',
});

const breadcrumbs = [
    { title: '首页', href: route('dashboard') },
    { title: '团队', href: route('teams.index') },
    { title: '创建团队', href: route('teams.create') },
] as { title: string; href: string }[];

const submit = () => {
    form.post(route('teams.store'));
};
</script>
