<template>
    <settings-layout :team="team">
        <div class="space-y-6">
            <div>
                <h3 class="text-lg font-medium">团队 AI 助手设置</h3>
                <p class="text-muted-foreground text-sm">自定义团队 AI 助手的行为和回答方式</p>
            </div>
            <Separator />

            <form @submit.prevent="saveSettings">
                <div class="mb-6">
                    <Label for="content" class="mb-2 block">团队 AI 上下文设置</Label>
                    <p class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                        设置团队的 AI 上下文，这些内容将在团队成员与 AI 对话时作为背景知识提供给 AI。
                        你可以在这里设置团队的工作流程、规范、术语或者其他团队相关的信息。
                    </p>
                    <Textarea
                        id="content"
                        v-model="form.content"
                        rows="6"
                        class="w-full"
                        placeholder="例如：我们团队使用敏捷开发方法，每周一进行周会，我们的代码规范要求..."
                    />
                    <InputError :message="form.errors.content" class="mt-2" />
                </div>

                <div class="mb-6">
                    <div class="flex items-center space-x-2">
                        <Checkbox id="is_active" v-model="form.is_active" />
                        <Label for="is_active">启用团队 AI 上下文</Label>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">如果禁用，则 AI 将不会使用团队上下文设置</p>
                </div>

                <div class="flex justify-end">
                    <Button type="submit" :disabled="form.processing">保存设置</Button>
                </div>
            </form>
        </div>
    </settings-layout>
</template>
<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import SettingsLayout from '@/layouts/teams/SettingsLayout.vue';
import { useForm } from '@inertiajs/vue3';
import { toast } from 'vue-sonner';

interface Team {
    id: number;
    name: string;
}

const props = defineProps({
    team: {
        type: Object as () => Team,
        required: true,
    },
    prompt: Object as any,
});

const form = useForm({
    content: props.prompt?.content || '',
    is_active: props.prompt.isActive,
});

const saveSettings = () => {
    form.post(route('teams.settings.ai-prompt.update', props.team.id), {
        onSuccess: () => {
            toast.success('团队 AI 上下文设置已保存');
        },
    });
};
</script>
