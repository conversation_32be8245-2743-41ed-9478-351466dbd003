<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workspace_users', function (Blueprint $table) {
            $table->id();

            // 工作空间 ID
            $table->foreignId('workspace_id')->constrained('workspaces')->onDelete('cascade');

            // 用户 ID
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');

            $table->timestamps();

            // 确保同一个用户不能重复加入同一个工作空间
            $table->unique(['workspace_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workspace_users');
    }
};
