<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AIMessage extends Model
{
    use HasFactory;

    protected $table = 'ai_messages';

    protected $fillable = [
        'conversation_id',
        'user_id',
        'role',
        'content',
        'metadata',
        'images',
    ];

    protected $casts = [
        'metadata' => 'json',
        'images' => 'json',
    ];

    /**
     * 获取消息的完整内容（包括文本和图片）
     *
     * @return array 格式化后的消息内容
     */
    public function getFormattedContent(): array
    {
        $formattedContent = [];

        // 添加文本内容
        if (! empty($this->content)) {
            $formattedContent[] = [
                'type' => 'text',
                'text' => $this->content,
            ];
        }

        // 添加图片内容
        $images = $this->images ?? [];
        foreach ($images as $image) {
            if (! empty($image['url']) && ! empty($image['mime_type'])) {
                $formattedContent[] = [
                    'type' => 'image_url',
                    'image_url' => [
                        'url' => $image['url'],
                        'detail' => 'high',
                    ],
                ];
            }
        }

        return $formattedContent;
    }

    /**
     * 添加图片到消息
     *
     * @param  string  $imageData  图片数据（base64）
     * @param  string  $mimeType  图片MIME类型
     */
    public function addImage(string $imageData, string $mimeType): void
    {
        $images = $this->images ?? [];

        $images[] = [
            'url' => $imageData,
            'mime_type' => $mimeType,
        ];

        $this->images = $images;
        $this->save();
    }

    /**
     * 获取第一张图片的URL
     */
    public function getFirstImageUrl(): ?string
    {
        $images = $this->images ?? [];

        if (empty($images)) {
            return null;
        }

        $firstImage = $images[0] ?? null;

        return $firstImage['url'] ?? null;
    }

    /**
     * 获取消息所属的对话
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(AIConversation::class, 'conversation_id', 'conversation_id');
    }

    /**
     * 获取消息所属的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取消息的函数调用
     */
    public function functionCalls(): HasMany
    {
        return $this->hasMany(AIFunctionCall::class, 'ai_message_id');
    }
}
