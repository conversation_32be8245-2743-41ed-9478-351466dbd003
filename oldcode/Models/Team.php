<?php

namespace App\Models;

use App\Traits\HasSettings;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Team extends Model
{
    use HasFactory, HasSettings, Notifiable;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'owner_id',
    ];

    /**
     * Get the owner of the team.
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the users that belong to the team.
     */
    public function users()
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    /**
     * Get the invitations for the team.
     */
    public function invitations()
    {
        return $this->hasMany(TeamInvitation::class);
    }

    public function allUsers()
    {
        return $this->users()->with('currentTeam')->get();
    }

    public function hasUser(User $user): bool
    {
        return $this->users()->where('user_id', $user->id)->exists();
    }
}
